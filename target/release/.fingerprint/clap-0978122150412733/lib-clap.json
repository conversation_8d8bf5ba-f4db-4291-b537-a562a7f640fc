{"rustc": 3590203003937835053, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 12724100863246979317, "profile": 2510022075885600103, "path": 6890001668245849655, "deps": [[6879013155829402151, "clap_derive", false, 10367114672136811776], [7429307784418916438, "clap_builder", false, 6381529653732420919]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap-0978122150412733/dep-lib-clap", "checksum": false}}], "rustflags": [], "metadata": 13636260659328210681, "config": 2202906307356721367, "compile_kind": 0}