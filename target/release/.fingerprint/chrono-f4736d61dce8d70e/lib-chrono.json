{"rustc": 3590203003937835053, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 3643947551994703751, "profile": 18277820415669657429, "path": 9315174952281663186, "deps": [[10448766010662481490, "num_traits", false, 10402738915269089952], [10633404241517405153, "serde", false, 7346462832942574077], [17958873330977204455, "iana_time_zone", false, 9339599887378893651]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/chrono-f4736d61dce8d70e/dep-lib-chrono", "checksum": false}}], "rustflags": [], "metadata": 9803565982372010724, "config": 2202906307356721367, "compile_kind": 0}