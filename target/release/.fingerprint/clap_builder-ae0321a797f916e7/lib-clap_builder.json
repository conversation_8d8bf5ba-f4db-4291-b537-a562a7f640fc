{"rustc": 3590203003937835053, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4540639333657397710, "profile": 2510022075885600103, "path": 4017678139884983003, "deps": [[967775003968733193, "strsim", false, 8588841920995693592], [1573621575349652083, "anstream", false, 10279174782440978752], [3950303651250106629, "clap_lex", false, 15738667289745608351], [15751993559584502049, "anstyle", false, 5512311438358159014]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_builder-ae0321a797f916e7/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "metadata": 13636260659328210681, "config": 2202906307356721367, "compile_kind": 0}