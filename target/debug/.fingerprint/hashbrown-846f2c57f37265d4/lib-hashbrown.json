{"rustc": 3590203003937835053, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 16815627201824848041, "profile": 3797293754785534760, "path": 13791360941256535429, "deps": [[2289252893304123003, "allocator_api2", false, 1311797664342026874], [7657838323326184233, "equivalent", false, 3018836763781108577], [9298736802812612957, "<PERSON><PERSON><PERSON>", false, 6437216732162913577]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-846f2c57f37265d4/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "metadata": 6228333144549390726, "config": 2202906307356721367, "compile_kind": 0}