{"rustc": 3590203003937835053, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 8830771204028428646, "profile": 3797293754785534760, "path": 13089252685028580185, "deps": [[2289252893304123003, "allocator_api2", false, 1311797664342026874], [5487915632734539349, "ahash", false, 13301566051244723165]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-845cc2ac0e01e038/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "metadata": 6228333144549390726, "config": 2202906307356721367, "compile_kind": 0}