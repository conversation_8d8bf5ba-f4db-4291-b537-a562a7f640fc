{"$message_type":"diagnostic","message":"unresolved imports `libc::nlattr`, `libc::nlmsgerr`, `libc::nlmsghdr`, `libc::sockaddr_nl`, `libc::AF_NETLINK`, `libc::ETH_P_ALL`, `libc::IFLA_XDP`, `libc::NETLINK_EXT_ACK`, `libc::NETLINK_ROUTE`, `libc::NLA_ALIGNTO`, `libc::NLA_F_NESTED`, `libc::NLA_TYPE_MASK`, `libc::NLMSG_DONE`, `libc::NLMSG_ERROR`, `libc::NLM_F_ACK`, `libc::NLM_F_CREATE`, `libc::NLM_F_DUMP`, `libc::NLM_F_ECHO`, `libc::NLM_F_EXCL`, `libc::NLM_F_MULTI`, `libc::NLM_F_REQUEST`, `libc::RTM_DELTFILTER`, `libc::RTM_GETTFILTER`, `libc::RTM_NEWQDISC`, `libc::RTM_NEWTFILTER`, `libc::RTM_SETLINK`, `libc::SOL_NETLINK`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":179,"byte_end":185,"line_start":10,"line_end":10,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"    getsockname, nlattr, nlmsgerr, nlmsghdr, recv, send, setsockopt, sockaddr_nl, socket,","highlight_start":18,"highlight_end":24}],"label":"no `nlattr` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":187,"byte_end":195,"line_start":10,"line_end":10,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"    getsockname, nlattr, nlmsgerr, nlmsghdr, recv, send, setsockopt, sockaddr_nl, socket,","highlight_start":26,"highlight_end":34}],"label":"no `nlmsgerr` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":197,"byte_end":205,"line_start":10,"line_end":10,"column_start":36,"column_end":44,"is_primary":true,"text":[{"text":"    getsockname, nlattr, nlmsgerr, nlmsghdr, recv, send, setsockopt, sockaddr_nl, socket,","highlight_start":36,"highlight_end":44}],"label":"no `nlmsghdr` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":231,"byte_end":242,"line_start":10,"line_end":10,"column_start":70,"column_end":81,"is_primary":true,"text":[{"text":"    getsockname, nlattr, nlmsgerr, nlmsghdr, recv, send, setsockopt, sockaddr_nl, socket,","highlight_start":70,"highlight_end":81}],"label":"no `sockaddr_nl` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":256,"byte_end":266,"line_start":11,"line_end":11,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    AF_NETLINK, AF_UNSPEC, ETH_P_ALL, IFF_UP, IFLA_XDP, NETLINK_EXT_ACK, NETLINK_ROUTE,","highlight_start":5,"highlight_end":15}],"label":"no `AF_NETLINK` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":279,"byte_end":288,"line_start":11,"line_end":11,"column_start":28,"column_end":37,"is_primary":true,"text":[{"text":"    AF_NETLINK, AF_UNSPEC, ETH_P_ALL, IFF_UP, IFLA_XDP, NETLINK_EXT_ACK, NETLINK_ROUTE,","highlight_start":28,"highlight_end":37}],"label":"no `ETH_P_ALL` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":298,"byte_end":306,"line_start":11,"line_end":11,"column_start":47,"column_end":55,"is_primary":true,"text":[{"text":"    AF_NETLINK, AF_UNSPEC, ETH_P_ALL, IFF_UP, IFLA_XDP, NETLINK_EXT_ACK, NETLINK_ROUTE,","highlight_start":47,"highlight_end":55}],"label":"no `IFLA_XDP` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":308,"byte_end":323,"line_start":11,"line_end":11,"column_start":57,"column_end":72,"is_primary":true,"text":[{"text":"    AF_NETLINK, AF_UNSPEC, ETH_P_ALL, IFF_UP, IFLA_XDP, NETLINK_EXT_ACK, NETLINK_ROUTE,","highlight_start":57,"highlight_end":72}],"label":"no `NETLINK_EXT_ACK` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":325,"byte_end":338,"line_start":11,"line_end":11,"column_start":74,"column_end":87,"is_primary":true,"text":[{"text":"    AF_NETLINK, AF_UNSPEC, ETH_P_ALL, IFF_UP, IFLA_XDP, NETLINK_EXT_ACK, NETLINK_ROUTE,","highlight_start":74,"highlight_end":87}],"label":"no `NETLINK_ROUTE` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":344,"byte_end":355,"line_start":12,"line_end":12,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    NLA_ALIGNTO, NLA_F_NESTED, NLA_TYPE_MASK, NLMSG_DONE, NLMSG_ERROR, NLM_F_ACK, NLM_F_CREATE,","highlight_start":5,"highlight_end":16}],"label":"no `NLA_ALIGNTO` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":357,"byte_end":369,"line_start":12,"line_end":12,"column_start":18,"column_end":30,"is_primary":true,"text":[{"text":"    NLA_ALIGNTO, NLA_F_NESTED, NLA_TYPE_MASK, NLMSG_DONE, NLMSG_ERROR, NLM_F_ACK, NLM_F_CREATE,","highlight_start":18,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":371,"byte_end":384,"line_start":12,"line_end":12,"column_start":32,"column_end":45,"is_primary":true,"text":[{"text":"    NLA_ALIGNTO, NLA_F_NESTED, NLA_TYPE_MASK, NLMSG_DONE, NLMSG_ERROR, NLM_F_ACK, NLM_F_CREATE,","highlight_start":32,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":386,"byte_end":396,"line_start":12,"line_end":12,"column_start":47,"column_end":57,"is_primary":true,"text":[{"text":"    NLA_ALIGNTO, NLA_F_NESTED, NLA_TYPE_MASK, NLMSG_DONE, NLMSG_ERROR, NLM_F_ACK, NLM_F_CREATE,","highlight_start":47,"highlight_end":57}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":398,"byte_end":409,"line_start":12,"line_end":12,"column_start":59,"column_end":70,"is_primary":true,"text":[{"text":"    NLA_ALIGNTO, NLA_F_NESTED, NLA_TYPE_MASK, NLMSG_DONE, NLMSG_ERROR, NLM_F_ACK, NLM_F_CREATE,","highlight_start":59,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":411,"byte_end":420,"line_start":12,"line_end":12,"column_start":72,"column_end":81,"is_primary":true,"text":[{"text":"    NLA_ALIGNTO, NLA_F_NESTED, NLA_TYPE_MASK, NLMSG_DONE, NLMSG_ERROR, NLM_F_ACK, NLM_F_CREATE,","highlight_start":72,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":422,"byte_end":434,"line_start":12,"line_end":12,"column_start":83,"column_end":95,"is_primary":true,"text":[{"text":"    NLA_ALIGNTO, NLA_F_NESTED, NLA_TYPE_MASK, NLMSG_DONE, NLMSG_ERROR, NLM_F_ACK, NLM_F_CREATE,","highlight_start":83,"highlight_end":95}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":440,"byte_end":450,"line_start":13,"line_end":13,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    NLM_F_DUMP, NLM_F_ECHO, NLM_F_EXCL, NLM_F_MULTI, NLM_F_REQUEST, RTM_DELTFILTER, RTM_GETTFILTER,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":452,"byte_end":462,"line_start":13,"line_end":13,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"    NLM_F_DUMP, NLM_F_ECHO, NLM_F_EXCL, NLM_F_MULTI, NLM_F_REQUEST, RTM_DELTFILTER, RTM_GETTFILTER,","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":464,"byte_end":474,"line_start":13,"line_end":13,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"    NLM_F_DUMP, NLM_F_ECHO, NLM_F_EXCL, NLM_F_MULTI, NLM_F_REQUEST, RTM_DELTFILTER, RTM_GETTFILTER,","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":476,"byte_end":487,"line_start":13,"line_end":13,"column_start":41,"column_end":52,"is_primary":true,"text":[{"text":"    NLM_F_DUMP, NLM_F_ECHO, NLM_F_EXCL, NLM_F_MULTI, NLM_F_REQUEST, RTM_DELTFILTER, RTM_GETTFILTER,","highlight_start":41,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":489,"byte_end":502,"line_start":13,"line_end":13,"column_start":54,"column_end":67,"is_primary":true,"text":[{"text":"    NLM_F_DUMP, NLM_F_ECHO, NLM_F_EXCL, NLM_F_MULTI, NLM_F_REQUEST, RTM_DELTFILTER, RTM_GETTFILTER,","highlight_start":54,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":504,"byte_end":518,"line_start":13,"line_end":13,"column_start":69,"column_end":83,"is_primary":true,"text":[{"text":"    NLM_F_DUMP, NLM_F_ECHO, NLM_F_EXCL, NLM_F_MULTI, NLM_F_REQUEST, RTM_DELTFILTER, RTM_GETTFILTER,","highlight_start":69,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":520,"byte_end":534,"line_start":13,"line_end":13,"column_start":85,"column_end":99,"is_primary":true,"text":[{"text":"    NLM_F_DUMP, NLM_F_ECHO, NLM_F_EXCL, NLM_F_MULTI, NLM_F_REQUEST, RTM_DELTFILTER, RTM_GETTFILTER,","highlight_start":85,"highlight_end":99}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":540,"byte_end":552,"line_start":14,"line_end":14,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    RTM_NEWQDISC, RTM_NEWTFILTER, RTM_SETLINK, SOCK_RAW, SOL_NETLINK,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":554,"byte_end":568,"line_start":14,"line_end":14,"column_start":19,"column_end":33,"is_primary":true,"text":[{"text":"    RTM_NEWQDISC, RTM_NEWTFILTER, RTM_SETLINK, SOCK_RAW, SOL_NETLINK,","highlight_start":19,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":570,"byte_end":581,"line_start":14,"line_end":14,"column_start":35,"column_end":46,"is_primary":true,"text":[{"text":"    RTM_NEWQDISC, RTM_NEWTFILTER, RTM_SETLINK, SOCK_RAW, SOL_NETLINK,","highlight_start":35,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":593,"byte_end":604,"line_start":14,"line_end":14,"column_start":58,"column_end":69,"is_primary":true,"text":[{"text":"    RTM_NEWQDISC, RTM_NEWTFILTER, RTM_SETLINK, SOCK_RAW, SOL_NETLINK,","highlight_start":58,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":197,"byte_end":205,"line_start":10,"line_end":10,"column_start":36,"column_end":44,"is_primary":true,"text":[{"text":"    getsockname, nlattr, nlmsgerr, nlmsghdr, recv, send, setsockopt, sockaddr_nl, socket,","highlight_start":36,"highlight_end":44}],"label":null,"suggested_replacement":"msghdr","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":231,"byte_end":242,"line_start":10,"line_end":10,"column_start":70,"column_end":81,"is_primary":true,"text":[{"text":"    getsockname, nlattr, nlmsgerr, nlmsghdr, recv, send, setsockopt, sockaddr_nl, socket,","highlight_start":70,"highlight_end":81}],"label":null,"suggested_replacement":"sockaddr_dl","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs","byte_start":256,"byte_end":266,"line_start":11,"line_end":11,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    AF_NETLINK, AF_UNSPEC, ETH_P_ALL, IFF_UP, IFLA_XDP, NETLINK_EXT_ACK, NETLINK_ROUTE,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"AF_IMPLINK","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved imports `libc::nlattr`, `libc::nlmsgerr`, `libc::nlmsghdr`, `libc::sockaddr_nl`, `libc::AF_NETLINK`, `libc::ETH_P_ALL`, `libc::IFLA_XDP`, `libc::NETLINK_EXT_ACK`, `libc::NETLINK_ROUTE`, `libc::NLA_ALIGNTO`, `libc::NLA_F_NESTED`, `libc::NLA_TYPE_MASK`, `libc::NLMSG_DONE`, `libc::NLMSG_ERROR`, `libc::NLM_F_ACK`, `libc::NLM_F_CREATE`, `libc::NLM_F_DUMP`, `libc::NLM_F_ECHO`, `libc::NLM_F_EXCL`, `libc::NLM_F_MULTI`, `libc::NLM_F_REQUEST`, `libc::RTM_DELTFILTER`, `libc::RTM_GETTFILTER`, `libc::RTM_NEWQDISC`, `libc::RTM_NEWTFILTER`, `libc::RTM_SETLINK`, `libc::SOL_NETLINK`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/netlink.rs:10:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    getsockname, nlattr, nlmsgerr, nlmsghdr, recv, send, setsockopt, sockaddr_nl, socket,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `sockaddr_nl` in the root\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `nlmsghdr` in the root\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `nlmsgerr` in the root\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `nlattr` in the root\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AF_NETLINK, AF_UNSPEC, ETH_P_ALL, IFF_UP, IFLA_XDP, NETLINK_EXT_ACK, NETLINK_ROUTE,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `NETLINK_ROUTE` in the root\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `NETLINK_EXT_ACK` in the root\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `IFLA_XDP` in the root\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `ETH_P_ALL` in the root\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `AF_NETLINK` in the root\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    NLA_ALIGNTO, NLA_F_NESTED, NLA_TYPE_MASK, NLMSG_DONE, NLMSG_ERROR, NLM_F_ACK, NLM_F_CREATE,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `NLA_ALIGNTO` in the root\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    NLM_F_DUMP, NLM_F_ECHO, NLM_F_EXCL, NLM_F_MULTI, NLM_F_REQUEST, RTM_DELTFILTER, RTM_GETTFILT\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    RTM_NEWQDISC, RTM_NEWTFILTER, RTM_SETLINK, SOCK_RAW, SOL_NETLINK,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a similar name exists in the module\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    getsockname, nlattr, nlmsgerr, \u001b[0m\u001b[0m\u001b[38;5;10mmsghdr\u001b[0m\u001b[0m, recv, send, setsockopt, sockaddr_nl, socket,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a similar name exists in the module\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    getsockname, nlattr, nlmsgerr, nlmsghdr, recv, send, setsockopt, \u001b[0m\u001b[0m\u001b[38;5;10msockaddr_dl\u001b[0m\u001b[0m, socket,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                      \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~~~~~~\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a similar name exists in the module\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10mAF_IMPLINK\u001b[0m\u001b[0m, AF_UNSPEC, ETH_P_ALL, IFF_UP, IFLA_XDP, NETLINK_EXT_ACK, NETLINK_ROUTE,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~~~~~\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved imports `libc::SYS_bpf`, `libc::SYS_perf_event_open`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/mod.rs","byte_start":243,"byte_end":250,"line_start":17,"line_end":17,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"use libc::{pid_t, SYS_bpf, SYS_perf_event_open};","highlight_start":19,"highlight_end":26}],"label":"no `SYS_bpf` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/mod.rs","byte_start":252,"byte_end":271,"line_start":17,"line_end":17,"column_start":28,"column_end":47,"is_primary":true,"text":[{"text":"use libc::{pid_t, SYS_bpf, SYS_perf_event_open};","highlight_start":28,"highlight_end":47}],"label":"no `SYS_perf_event_open` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved imports `libc::SYS_bpf`, `libc::SYS_perf_event_open`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/mod.rs:17:19\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse libc::{pid_t, SYS_bpf, SYS_perf_event_open};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `SYS_perf_event_open` in the root\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `SYS_bpf` in the root\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find value `CLOCK_BOOTTIME` in crate `libc`","code":{"code":"E0425","explanation":"An unresolved name was used.\n\nErroneous code examples:\n\n```compile_fail,E0425\nsomething_that_doesnt_exist::foo;\n// error: unresolved name `something_that_doesnt_exist::foo`\n\n// or:\n\ntrait Foo {\n    fn bar() {\n        Self; // error: unresolved name `Self`\n    }\n}\n\n// or:\n\nlet x = unknown_variable;  // error: unresolved name `unknown_variable`\n```\n\nPlease verify that the name wasn't misspelled and ensure that the\nidentifier being referred to is valid for the given situation. Example:\n\n```\nenum something_that_does_exist {\n    Foo,\n}\n```\n\nOr:\n\n```\nmod something_that_does_exist {\n    pub static foo : i32 = 0i32;\n}\n\nsomething_that_does_exist::foo; // ok!\n```\n\nOr:\n\n```\nlet unknown_variable = 12u32;\nlet x = unknown_variable; // ok!\n```\n\nIf the item is not defined in the current module, it must be imported using a\n`use` statement, like so:\n\n```\n# mod foo { pub fn bar() {} }\n# fn main() {\nuse foo::bar;\nbar();\n# }\n```\n\nIf the item you are importing is not defined in some super-module of the\ncurrent module, then it must also be declared as public (e.g., `pub fn`).\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/libc-0.2.174/src/unix/bsd/apple/mod.rs","byte_start":95787,"byte_end":95829,"line_start":2829,"line_end":2829,"column_start":1,"column_end":43,"is_primary":false,"text":[{"text":"pub const CLOCK_REALTIME: crate::clockid_t = 0;","highlight_start":1,"highlight_end":43}],"label":"similarly named constant `CLOCK_REALTIME` defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/programs/utils.rs","byte_start":2638,"byte_end":2652,"line_start":75,"line_end":75,"column_start":37,"column_end":51,"is_primary":true,"text":[{"text":"    let since_boot = get_time(libc::CLOCK_BOOTTIME);","highlight_start":37,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"a constant with a similar name exists","code":null,"level":"help","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/programs/utils.rs","byte_start":2638,"byte_end":2652,"line_start":75,"line_end":75,"column_start":37,"column_end":51,"is_primary":true,"text":[{"text":"    let since_boot = get_time(libc::CLOCK_BOOTTIME);","highlight_start":37,"highlight_end":51}],"label":null,"suggested_replacement":"CLOCK_REALTIME","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0425]\u001b[0m\u001b[0m\u001b[1m: cannot find value `CLOCK_BOOTTIME` in crate `libc`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/programs/utils.rs:75:37\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m75\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let since_boot = get_time(libc::CLOCK_BOOTTIME);\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: a constant with a similar name exists: `CLOCK_REALTIME`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/libc-0.2.174/src/unix/bsd/apple/mod.rs:2829:1\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2829\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub const CLOCK_REALTIME: crate::clockid_t = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12msimilarly named constant `CLOCK_REALTIME` defined here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`match` arms have incompatible types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/mod.rs","byte_start":3052,"byte_end":3055,"line_start":110,"line_end":110,"column_start":17,"column_end":20,"is_primary":true,"text":[{"text":"                int","highlight_start":17,"highlight_end":20}],"label":"expected `i32`, found `i64`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/mod.rs","byte_start":2489,"byte_end":2550,"line_start":97,"line_end":97,"column_start":17,"column_end":78,"is_primary":false,"text":[{"text":"                libc::syscall(SYS_bpf, cmd, attr, mem::size_of::<bpf_attr>())","highlight_start":17,"highlight_end":78}],"label":"this is found to be of type `i32`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/mod.rs","byte_start":2729,"byte_end":2794,"line_start":105,"line_end":105,"column_start":18,"column_end":83,"is_primary":false,"text":[{"text":"            } => libc::syscall(SYS_perf_event_open, &attr, pid, cpu, group, flags),","highlight_start":18,"highlight_end":83}],"label":"this is found to be of type `i32`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/mod.rs","byte_start":2416,"byte_end":3079,"line_start":95,"line_end":112,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        match call {","highlight_start":9,"highlight_end":21},{"text":"            Syscall::Bpf { cmd, attr } => {","highlight_start":1,"highlight_end":44},{"text":"                libc::syscall(SYS_bpf, cmd, attr, mem::size_of::<bpf_attr>())","highlight_start":1,"highlight_end":78},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"            Syscall::PerfEventOpen {","highlight_start":1,"highlight_end":37},{"text":"                attr,","highlight_start":1,"highlight_end":22},{"text":"                pid,","highlight_start":1,"highlight_end":21},{"text":"                cpu,","highlight_start":1,"highlight_end":21},{"text":"                group,","highlight_start":1,"highlight_end":23},{"text":"                flags,","highlight_start":1,"highlight_end":23},{"text":"            } => libc::syscall(SYS_perf_event_open, &attr, pid, cpu, group, flags),","highlight_start":1,"highlight_end":84},{"text":"            Syscall::PerfEventIoctl { fd, request, arg } => {","highlight_start":1,"highlight_end":62},{"text":"                let int = libc::ioctl(fd.as_raw_fd(), request.try_into().unwrap(), arg);","highlight_start":1,"highlight_end":89},{"text":"                #[allow(trivial_numeric_casts)]","highlight_start":1,"highlight_end":48},{"text":"                let int = int as c_long;","highlight_start":1,"highlight_end":41},{"text":"                int","highlight_start":1,"highlight_end":20},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        }","highlight_start":1,"highlight_end":10}],"label":"`match` arms have incompatible types","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"you can convert an `i64` to an `i32` and panic if the converted value doesn't fit","code":null,"level":"help","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/mod.rs","byte_start":3055,"byte_end":3055,"line_start":110,"line_end":110,"column_start":20,"column_end":20,"is_primary":true,"text":[{"text":"                int","highlight_start":20,"highlight_end":20}],"label":null,"suggested_replacement":".try_into().unwrap()","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: `match` arms have incompatible types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/aya-0.12.0/src/sys/mod.rs:110:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m95\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        match call {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m96\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Syscall::Bpf { cmd, attr } => {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m97\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                libc::syscall(SYS_bpf, cmd, attr, mem::size_of::<bpf_attr>())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is found to be of type `i32`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m98\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            }\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            } => libc::syscall(SYS_perf_event_open, &attr, pid, cpu, group, flag\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis is found to be of type `i32`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                int\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `i32`, found `i64`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            }\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m112\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m`match` arms have incompatible types\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: you can convert an `i64` to an `i32` and panic if the converted value doesn't fit\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                int\u001b[0m\u001b[0m\u001b[38;5;10m.try_into().unwrap()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 4 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 4 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0308, E0425, E0432.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0308, E0425, E0432.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0308`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0308`.\u001b[0m\n"}
