{"rustc": 3590203003937835053, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 9315256552433306347, "profile": 3797293754785534760, "path": 5028181193645106006, "deps": [[2964536209444415731, "memchr", false, 12760594004560297948], [6314779025451150414, "regex_automata", false, 14301059695984071096], [7325384046744447800, "aho_corasick", false, 14932739334821945676], [9111760993595911334, "regex_syntax", false, 2677885332793953411]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-564efb513671da9b/dep-lib-regex", "checksum": false}}], "rustflags": [], "metadata": 3256615787768725874, "config": 2202906307356721367, "compile_kind": 0}