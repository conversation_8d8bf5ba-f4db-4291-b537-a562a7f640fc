{"rustc": 3590203003937835053, "features": "[\"coff\", \"elf\", \"macho\", \"pe\", \"read_core\", \"std\", \"write\", \"write_core\", \"write_std\", \"xcoff\"]", "declared_features": "[\"all\", \"alloc\", \"archive\", \"build\", \"build_core\", \"cargo-all\", \"coff\", \"compiler_builtins\", \"compression\", \"core\", \"default\", \"doc\", \"elf\", \"macho\", \"pe\", \"read\", \"read_core\", \"rustc-dep-of-std\", \"std\", \"unaligned\", \"unstable\", \"unstable-all\", \"wasm\", \"write\", \"write_core\", \"write_std\", \"xcoff\"]", "target": 8701048939846490694, "profile": 3797293754785534760, "path": 17632701347778968395, "deps": [[2964536209444415731, "memchr", false, 12760594004560297948], [2989785442755699193, "crc32fast", false, 5613174733599917096], [9100959953705921898, "build_script_build", false, 7820146913502348035], [13498915242333187431, "indexmap", false, 6316006787385724365], [16375999175142029690, "hashbrown", false, 5151292897651080338]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/object-1f2caad034a44a7a/dep-lib-object", "checksum": false}}], "rustflags": [], "metadata": 2023933971217488582, "config": 2202906307356721367, "compile_kind": 0}