{"rustc": 3590203003937835053, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 14790166133050072309, "profile": 1200860260873630964, "path": 18371874638050564532, "deps": [[7006636483571730090, "unicode_ident", false, 10071055100003983464], [17525013869477438691, "quote", false, 6965500794337840241], [18036439996138669183, "proc_macro2", false, 886901636489571277]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-002f1c886a95ff63/dep-lib-syn", "checksum": false}}], "rustflags": [], "metadata": 6886477143387768027, "config": 2202906307356721367, "compile_kind": 0}