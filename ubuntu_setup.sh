#!/bin/bash

echo "🐧 eBPF File Monitor - Ubuntu Setup Script"
echo "==========================================="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check kernel version
check_kernel_version() {
    echo -e "${BLUE}🔍 Checking kernel version...${NC}"
    kernel_version=$(uname -r)
    echo "Current kernel: $kernel_version"
    
    # Extract major and minor version
    major=$(echo $kernel_version | cut -d. -f1)
    minor=$(echo $kernel_version | cut -d. -f2)
    
    if [ "$major" -gt 4 ] || ([ "$major" -eq 4 ] && [ "$minor" -ge 18 ]); then
        echo -e "${GREEN}✅ Kernel version $kernel_version supports eBPF${NC}"
        return 0
    else
        echo -e "${RED}❌ Kernel version $kernel_version is too old for eBPF (need 4.18+)${NC}"
        return 1
    fi
}

# Function to check if running as root
check_root() {
    if [ "$EUID" -eq 0 ]; then
        echo -e "${GREEN}✅ Running as root - can load eBPF programs${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  Not running as root - will need sudo for eBPF operations${NC}"
        return 1
    fi
}

# Function to check eBPF support
check_ebpf_support() {
    echo -e "${BLUE}🔍 Checking eBPF support...${NC}"
    
    if [ -f /proc/sys/kernel/unprivileged_bpf_disabled ]; then
        echo -e "${GREEN}✅ eBPF syscall is available${NC}"
    else
        echo -e "${RED}❌ eBPF syscall not available${NC}"
        return 1
    fi
    
    if command_exists bpftool; then
        echo -e "${GREEN}✅ bpftool is available${NC}"
    else
        echo -e "${YELLOW}⚠️  bpftool not found - will install later${NC}"
    fi
}

# Function to install dependencies
install_dependencies() {
    echo -e "${BLUE}📦 Installing dependencies...${NC}"
    
    # Update package list
    sudo apt update
    
    # Install required packages
    sudo apt install -y \
        build-essential \
        clang \
        llvm \
        libbpf-dev \
        linux-headers-$(uname -r) \
        pkg-config \
        libssl-dev \
        curl \
        git \
        linux-tools-$(uname -r) \
        linux-tools-common \
        linux-tools-generic
    
    # Install Rust if not present
    if ! command_exists rustc; then
        echo -e "${BLUE}🦀 Installing Rust...${NC}"
        curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
        source ~/.cargo/env
    fi
    
    # Add eBPF target
    rustup target add bpfel-unknown-none
    
    # Install bpf-linker
    cargo install bpf-linker
    
    echo -e "${GREEN}✅ Dependencies installed${NC}"
}

# Function to create production version
create_production_version() {
    echo -e "${BLUE}🏭 Creating production version...${NC}"
    
    # Create production Cargo.toml
    cat > Cargo.toml << EOF
[package]
name = "file-monitor-ebpf"
version = "0.1.0"
edition = "2021"

[dependencies]
aya = { version = "0.12", features = ["async_tokio"] }
aya-log = "0.2"
clap = { version = "4.0", features = ["derive"] }
env_logger = "0.10"
log = "0.4"
tokio = { version = "1.0", features = ["macros", "rt", "rt-multi-thread", "net", "signal", "time"] }
anyhow = "1.0"
bytes = "1.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }

[build-dependencies]
aya-tool = "0.12"

[[bin]]
name = "file-monitor-ebpf"
path = "src/main.rs"
EOF

    # Create production build.rs
    cat > build.rs << 'EOF'
use std::env;
use std::path::PathBuf;

fn main() {
    let out_dir = PathBuf::from(env::var_os("OUT_DIR").unwrap());
    
    println!("cargo:rerun-if-changed=src/file_monitor.bpf.c");
    
    let arch = env::var("CARGO_CFG_TARGET_ARCH").unwrap();
    let target = if arch == "x86_64" {
        "bpfel-unknown-none"
    } else if arch == "aarch64" {
        "bpfel-unknown-none"
    } else {
        panic!("Unsupported architecture: {}", arch);
    };
    
    // Compile eBPF program
    let status = std::process::Command::new("clang")
        .args([
            "-target", "bpf",
            "-O2", "-g",
            "-c", "src/file_monitor.bpf.c",
            "-o", &format!("{}/file_monitor.bpf.o", out_dir.display())
        ])
        .status()
        .expect("Failed to compile eBPF program");
    
    if !status.success() {
        panic!("Failed to compile eBPF program");
    }
}
EOF

    echo -e "${GREEN}✅ Production version created${NC}"
}

# Main execution
main() {
    echo "🔧 SYSTEM REQUIREMENTS CHECK"
    echo "============================"
    
    check_kernel_version
    kernel_ok=$?
    
    check_root
    root_ok=$?
    
    check_ebpf_support
    ebpf_ok=$?
    
    echo
    echo "📋 SUMMARY"
    echo "=========="
    
    if [ $kernel_ok -eq 0 ] && [ $ebpf_ok -eq 0 ]; then
        echo -e "${GREEN}✅ System is ready for eBPF development${NC}"
        
        read -p "Install dependencies? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            install_dependencies
            create_production_version
            
            echo
            echo -e "${GREEN}🎉 Setup complete!${NC}"
            echo
            echo "📝 Next steps:"
            echo "1. Build the project: cargo build --release"
            echo "2. Run the monitor: sudo ./target/release/file-monitor-ebpf"
            echo "3. Run production: sudo ./target/release/file-monitor-ebpf"
            echo
            echo "⚠️  Note: Production mode requires root privileges"
        fi
    else
        echo -e "${RED}❌ System needs updates before running eBPF programs${NC}"
        echo
        echo "🔧 Required fixes:"
        [ $kernel_ok -ne 0 ] && echo "  • Update kernel to 4.18 or higher"
        [ $ebpf_ok -ne 0 ] && echo "  • Install eBPF support packages"
        echo "  • Install dependencies with: sudo apt install linux-headers-\$(uname -r) libbpf-dev"
    fi
}

# Run main function
main "$@"
