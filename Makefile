# Makefile for eBPF File Monitor

.PHONY: all build clean install run help

# Default target
all: build

# Build the project
build:
	@echo "Building eBPF file monitor..."
	cargo build --release

# Build debug version
debug:
	@echo "Building debug version..."
	cargo build

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	cargo clean
	rm -f target/file_monitor.bpf.o

# Install Rust eBPF toolchain
install-deps:
	@echo "Installing eBPF toolchain..."
	rustup target add bpfel-unknown-none
	cargo install bpf-linker

# Run the application (requires root)
run:
	@echo "Running file monitor (requires root)..."
	sudo ./target/release/file-monitor-ebpf

# Run with verbose logging
run-verbose:
	@echo "Running file monitor with verbose logging..."
	sudo RUST_LOG=debug ./target/release/file-monitor-ebpf --verbose

# Check if we're on Linux
check-linux:
	@if [ "$$(uname)" != "Linux" ]; then \
		echo "Warning: This application is designed for Linux systems."; \
		echo "Consider using a Linux VM for development and testing."; \
	fi

# Install Linux dependencies (Ubuntu/Debian)
install-linux-deps:
	@echo "Installing Linux dependencies..."
	sudo apt update
	sudo apt install -y clang llvm libbpf-dev linux-headers-$(shell uname -r) build-essential

# Install Linux dependencies (RHEL/CentOS/Fedora)
install-linux-deps-rhel:
	@echo "Installing Linux dependencies (RHEL/CentOS/Fedora)..."
	sudo dnf install -y clang llvm libbpf-devel kernel-headers kernel-devel



# Format code
fmt:
	@echo "Formatting code..."
	cargo fmt

# Run clippy linter
clippy:
	@echo "Running clippy linter..."
	cargo clippy

# Run tests
test:
	@echo "Running tests..."
	cargo test

# Help target
help:
	@echo "Available targets:"
	@echo "  build              - Build the release version"
	@echo "  debug              - Build the debug version"
	@echo "  clean              - Clean build artifacts"
	@echo "  install-deps       - Install Rust eBPF toolchain"
	@echo "  run                - Run the application (requires root)"
	@echo "  run-verbose        - Run with verbose logging"
	@echo "  check-linux        - Check if running on Linux"
	@echo "  install-linux-deps - Install Linux dependencies (Ubuntu/Debian)"
	@echo "  install-linux-deps-rhel - Install Linux dependencies (RHEL/CentOS/Fedora)"

	@echo "  fmt                - Format code"
	@echo "  clippy             - Run clippy linter"
	@echo "  test               - Run tests"
	@echo "  help               - Show this help message"
