# eBPF File Monitor

A comprehensive file monitoring application using eBPF in Rust that tracks file operations and login events.

## Features

- **File Operations Monitoring**: Tracks file open, write, and delete operations
- **Login Event Monitoring**: Monitors user login events through setuid syscalls
- **Real-time Events**: Displays events in real-time with timestamps
- **User Information**: Shows which user (username, UID, GID) performed each operation
- **Process Information**: Displays process ID and command name for each event
- **JSON Logging**: Structured logging for integration with other systems

## Prerequisites

### macOS

Since this is designed for Linux eBPF programs, you'll need to run this on a Linux system or use a Linux VM. On macOS, you can use:

1. **Virtual Machine** (VirtualBox, VMware, etc.)

2. **WSL2** (if on Windows)

### Linux Requirements

- Linux kernel 4.18+ (preferably 5.4+)
- Root privileges (for loading eBPF programs)
- Build tools: `clang`, `llvm`, `libbpf-dev`

#### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install -y clang llvm libbpf-dev linux-headers-$(uname -r) build-essential
```

#### RHEL/CentOS/Fedora:
```bash
sudo dnf install -y clang llvm libbpf-devel kernel-headers kernel-devel
```

## Building

1. **Install Rust and eBPF tools**:
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   rustup target add bpfel-unknown-none
   cargo install bpf-linker
   ```

2. **Clone or create the project**:
   ```bash
   # If you have the source code
   cd file-monitor-ebpf
   
   # Build the project
   cargo build --release
   ```

3. **Compile the eBPF program**:
   ```bash
   # The build script should handle this automatically
   # But if needed, you can compile manually:
   clang -target bpf -O2 -g -c src/file_monitor.bpf.c -o target/file_monitor.bpf.o
   ```

## Running

**Important**: This program requires root privileges to load eBPF programs.

### Basic Usage

```bash
sudo ./target/release/file-monitor-ebpf
```

### With Verbose Logging

```bash
sudo RUST_LOG=debug ./target/release/file-monitor-ebpf --verbose
```

### Example Output

```
[FILE EVENT #1] 2024-01-15 14:30:22 UTC - WRITE by alice (PID: 1234, UID: 1000, GID: 1000): /home/<USER>/document.txt
[FILE EVENT #2] 2024-01-15 14:30:25 UTC - DELETE by bob (PID: 1256, UID: 1001, GID: 1001): /tmp/tempfile.log
[LOGIN EVENT #1] 2024-01-15 14:30:30 UTC - User 'charlie' logged in (PID: 1267, UID: 1002, Session: 1267)
[FILE EVENT #3] 2024-01-15 14:30:35 UTC - OPEN by root (PID: 1280, UID: 0, GID: 0): /etc/passwd
```

## Architecture

### Components

1. **Userspace Application (`src/main.rs`)**:
   - Loads and manages eBPF programs
   - Processes events from kernel space
   - Formats and displays events
   - Provides CLI interface

2. **eBPF Program (`src/file_monitor.bpf.c`)**:
   - Runs in kernel space
   - Hooks into kernel functions and tracepoints
   - Collects file and login events
   - Stores events in eBPF maps

3. **Event Processing**:
   - File events: Open, Write, Delete operations
   - Login events: User authentication via setuid syscalls
   - Real-time event streaming from kernel to userspace

### eBPF Hooks

- **`kprobe/do_sys_open`**: Monitors file open operations
- **`kprobe/vfs_write`**: Monitors file write operations  
- **`kprobe/vfs_unlink`**: Monitors file delete operations
- **`tracepoint/syscalls/sys_enter_setuid`**: Monitors login events

## Security Considerations

- **Root Privileges**: Required for loading eBPF programs
- **Kernel Access**: Direct access to kernel events and data structures
- **Performance**: Minimal overhead due to eBPF efficiency
- **Filtering**: Consider adding filters for high-volume environments

## Limitations

- **Linux Only**: eBPF is Linux-specific
- **Kernel Version**: Requires modern Linux kernel (4.18+)
- **Root Required**: Must run as root for eBPF program loading
- **macOS**: This example won't work on macOS as it uses Linux-specific eBPF features

## Extending the Monitor

### Adding New Event Types

1. Define event structure in both C and Rust
2. Add new eBPF program section
3. Update maps and processing logic
4. Add event handling in userspace

### Custom Filters

You can add filters to reduce noise:

```rust
// Example: Filter by specific UIDs
if event.uid < 1000 {
    return; // Skip system users
}

// Example: Filter by file extensions
if !event.filename.ends_with(".txt") {
    return; // Only monitor text files
}
```

## Troubleshooting

### Common Issues

1. **Permission Denied**: Run with `sudo`
2. **eBPF Load Failed**: Check kernel version and eBPF support
3. **No Events**: Verify eBPF program is properly attached
4. **Build Errors**: Ensure all dependencies are installed

### Debugging

Enable verbose logging:
```bash
sudo RUST_LOG=debug ./target/release/file-monitor-ebpf --verbose
```

Check eBPF program status:
```bash
sudo bpftool prog list
sudo bpftool map list
```

## License

This project is licensed under the GPL License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## References

- [eBPF Documentation](https://ebpf.io/)
- [Aya Framework](https://github.com/aya-rs/aya)
- [Linux Kernel eBPF](https://www.kernel.org/doc/html/latest/bpf/index.html)
- [BPF Helpers](https://man7.org/linux/man-pages/man7/bpf-helpers.7.html)
