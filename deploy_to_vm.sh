#!/bin/bash

# Deploy script for eBPF File Monitor to Ubuntu VM
# Usage: ./deploy_to_vm.sh

VM_HOST="ubuntu@***********"
KEY_PATH="/Users/<USER>/Downloads/awskeypair.pem"
PROJECT_NAME="file-monitor-ebpf"

echo "🚀 Deploying eBPF File Monitor to Ubuntu VM"
echo "============================================"

# Create a temporary directory with only the necessary files
TEMP_DIR=$(mktemp -d)
echo "📦 Preparing files in $TEMP_DIR"

# Copy essential files
cp Cargo.toml "$TEMP_DIR/"
cp build.rs "$TEMP_DIR/"
cp Makefile "$TEMP_DIR/"
cp README.md "$TEMP_DIR/"
cp -r src "$TEMP_DIR/"

# Create the remote setup script
cat > "$TEMP_DIR/setup_on_vm.sh" << 'EOF'
#!/bin/bash

echo "🐧 Setting up eBPF File Monitor on Ubuntu VM"
echo "============================================="

# Update system
sudo apt update

# Install dependencies
echo "📦 Installing dependencies..."
sudo apt install -y \
    build-essential \
    clang \
    llvm \
    libbpf-dev \
    linux-headers-$(uname -r) \
    pkg-config \
    libssl-dev \
    curl \
    git

# Install Rust if not present
if ! command -v rustc &> /dev/null; then
    echo "🦀 Installing Rust..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
fi

# Install and set nightly toolchain (required for eBPF)
echo "🌙 Installing Rust nightly toolchain..."
rustup install nightly
rustup default nightly

# Add eBPF target (only available on nightly)
echo "🎯 Adding eBPF target..."
rustup target add bpfel-unknown-none

# Install bpf-linker
echo "🔗 Installing bpf-linker..."
cargo install bpf-linker

echo "✅ Dependencies installed successfully"
echo ""
echo "🔨 Building the project..."
cargo build --release

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo ""
    echo "🏃 To run the file monitor:"
    echo "sudo ./target/release/file-monitor-ebpf"
    echo ""
    echo "📊 Available options:"
    echo "  -v, --verbose     Enable verbose output with JSON logging"
    echo "  -f, --filter      Filter events by file extension (e.g., 'txt,log,conf')"
    echo "  -o, --output      Output format: text or json (default: text)"
    echo "      --min-uid     Minimum UID to monitor (default: 1000)"
else
    echo "❌ Build failed. Check the error messages above."
fi
EOF

chmod +x "$TEMP_DIR/setup_on_vm.sh"

# Copy files to VM
echo "📤 Copying files to VM..."
scp -i "$KEY_PATH" -r "$TEMP_DIR"/* "$VM_HOST:~/"

if [ $? -eq 0 ]; then
    echo "✅ Files copied successfully"
    echo ""
    echo "🔧 Now SSH into your VM and run the setup:"
    echo "ssh -i $KEY_PATH $VM_HOST"
    echo "chmod +x setup_on_vm.sh"
    echo "./setup_on_vm.sh"
    echo ""
    echo "🏃 After setup, run the monitor with:"
    echo "sudo ./target/release/file-monitor-ebpf"
else
    echo "❌ Failed to copy files to VM"
    exit 1
fi

# Cleanup
rm -rf "$TEMP_DIR"
echo "🧹 Cleaned up temporary files"
