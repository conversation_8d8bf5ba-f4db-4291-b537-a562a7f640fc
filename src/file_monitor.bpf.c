#include <linux/bpf.h>
#include <linux/ptrace.h>
#include <linux/sched.h>
#include <linux/fs.h>
#include <linux/dcache.h>
#include <linux/mount.h>
#include <linux/path.h>
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>

#define MAX_FILENAME_LEN 256
#define MAX_COMM_LEN 16
#define MAX_USERNAME_LEN 32

struct file_event_data {
    __u32 pid;
    __u32 uid;
    __u32 gid;
    char comm[MAX_COMM_LEN];
    char filename[MAX_FILENAME_LEN];
    __u32 event_type;
    __u64 timestamp;
};

struct login_event_data {
    __u32 pid;
    __u32 uid;
    char username[MAX_USERNAME_LEN];
    __u32 session_id;
    __u32 event_type;
    __u64 timestamp;
};

// Maps for storing events
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, __u32);
    __type(value, struct file_event_data);
} FILE_EVENTS SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, __u32);
    __type(value, struct login_event_data);
} LOGIN_EVENTS SEC(".maps");

// Counter for event keys
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 2);
    __type(key, __u32);
    __type(value, __u32);
} COUNTERS SEC(".maps");

static __always_inline int get_file_path(struct file *file, char *buf, size_t size) {
    struct dentry *dentry = file->f_path.dentry;
    struct mount *mnt = file->f_path.mnt;
    
    if (!dentry || !mnt) {
        return -1;
    }
    
    // Simple approach: get the dentry name
    const char *name = dentry->d_name.name;
    if (!name) {
        return -1;
    }
    
    bpf_probe_read_kernel_str(buf, size, name);
    return 0;
}

static __always_inline void populate_file_event(struct file_event_data *event, 
                                               struct file *file, 
                                               __u32 event_type) {
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    __u64 uid_gid = bpf_get_current_uid_gid();
    
    event->pid = pid_tgid >> 32;
    event->uid = uid_gid & 0xFFFFFFFF;
    event->gid = uid_gid >> 32;
    event->event_type = event_type;
    event->timestamp = bpf_ktime_get_ns();
    
    bpf_get_current_comm(&event->comm, sizeof(event->comm));
    
    // Get filename
    if (get_file_path(file, event->filename, sizeof(event->filename)) < 0) {
        bpf_probe_read_kernel_str(event->filename, sizeof(event->filename), "unknown");
    }
}

// File open monitoring
SEC("kprobe/do_sys_open")
int file_open(struct pt_regs *ctx) {
    struct file_event_data event = {};
    struct file *file = (struct file *)PT_REGS_RC(ctx);
    
    if (!file) {
        return 0;
    }
    
    populate_file_event(&event, file, 0); // 0 = OPEN
    
    __u32 counter_key = 0;
    __u32 *counter = bpf_map_lookup_elem(&COUNTERS, &counter_key);
    if (counter) {
        __u32 event_key = *counter;
        bpf_map_update_elem(&FILE_EVENTS, &event_key, &event, BPF_ANY);
        (*counter)++;
        bpf_map_update_elem(&COUNTERS, &counter_key, counter, BPF_ANY);
    }
    
    return 0;
}

// File write monitoring
SEC("kprobe/vfs_write")
int file_write(struct pt_regs *ctx) {
    struct file_event_data event = {};
    struct file *file = (struct file *)PT_REGS_PARM1(ctx);
    
    if (!file) {
        return 0;
    }
    
    populate_file_event(&event, file, 1); // 1 = WRITE
    
    __u32 counter_key = 0;
    __u32 *counter = bpf_map_lookup_elem(&COUNTERS, &counter_key);
    if (counter) {
        __u32 event_key = *counter;
        bpf_map_update_elem(&FILE_EVENTS, &event_key, &event, BPF_ANY);
        (*counter)++;
        bpf_map_update_elem(&COUNTERS, &counter_key, counter, BPF_ANY);
    }
    
    return 0;
}

// File delete monitoring
SEC("kprobe/vfs_unlink")
int file_delete(struct pt_regs *ctx) {
    struct file_event_data event = {};
    struct inode *dir = (struct inode *)PT_REGS_PARM1(ctx);
    struct dentry *dentry = (struct dentry *)PT_REGS_PARM2(ctx);
    
    if (!dentry) {
        return 0;
    }
    
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    __u64 uid_gid = bpf_get_current_uid_gid();
    
    event.pid = pid_tgid >> 32;
    event.uid = uid_gid & 0xFFFFFFFF;
    event.gid = uid_gid >> 32;
    event.event_type = 2; // 2 = DELETE
    event.timestamp = bpf_ktime_get_ns();
    
    bpf_get_current_comm(&event.comm, sizeof(event.comm));
    
    // Get filename from dentry
    const char *name = dentry->d_name.name;
    if (name) {
        bpf_probe_read_kernel_str(event.filename, sizeof(event.filename), name);
    } else {
        bpf_probe_read_kernel_str(event.filename, sizeof(event.filename), "unknown");
    }
    
    __u32 counter_key = 0;
    __u32 *counter = bpf_map_lookup_elem(&COUNTERS, &counter_key);
    if (counter) {
        __u32 event_key = *counter;
        bpf_map_update_elem(&FILE_EVENTS, &event_key, &event, BPF_ANY);
        (*counter)++;
        bpf_map_update_elem(&COUNTERS, &counter_key, counter, BPF_ANY);
    }
    
    return 0;
}

// Login monitoring
SEC("tracepoint/syscalls/sys_enter_setuid")
int login_monitor(struct trace_event_raw_sys_enter *ctx) {
    struct login_event_data event = {};
    
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    __u64 uid_gid = bpf_get_current_uid_gid();
    
    event.pid = pid_tgid >> 32;
    event.uid = uid_gid & 0xFFFFFFFF;
    event.session_id = pid_tgid & 0xFFFFFFFF;
    event.event_type = 0; // 0 = LOGIN
    event.timestamp = bpf_ktime_get_ns();
    
    // Get username from current task
    struct task_struct *task = (struct task_struct *)bpf_get_current_task();
    if (task) {
        bpf_probe_read_kernel_str(event.username, sizeof(event.username), task->comm);
    }
    
    __u32 counter_key = 1;
    __u32 *counter = bpf_map_lookup_elem(&COUNTERS, &counter_key);
    if (counter) {
        __u32 event_key = *counter;
        bpf_map_update_elem(&LOGIN_EVENTS, &event_key, &event, BPF_ANY);
        (*counter)++;
        bpf_map_update_elem(&COUNTERS, &counter_key, counter, BPF_ANY);
    }
    
    return 0;
}

char LICENSE[] SEC("license") = "GPL";
