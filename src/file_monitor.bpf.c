#include <linux/bpf.h>
#include <linux/ptrace.h>
#include <linux/sched.h>
#include <linux/fs.h>
#include <linux/dcache.h>
#include <linux/mount.h>
#include <linux/path.h>
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>

#define MAX_FILENAME_LEN 256
#define MAX_COMM_LEN 16
#define MAX_USERNAME_LEN 32

struct file_event_data {
    __u32 pid;
    __u32 uid;
    __u32 gid;
    char comm[MAX_COMM_LEN];
    char filename[MAX_FILENAME_LEN];
    __u32 event_type;
    __u64 timestamp;
};

struct login_event_data {
    __u32 pid;
    __u32 uid;
    char username[MAX_USERNAME_LEN];
    __u32 session_id;
    __u32 event_type;
    __u64 timestamp;
};

// Maps for storing events
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, __u32);
    __type(value, struct file_event_data);
} FILE_EVENTS SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, __u32);
    __type(value, struct login_event_data);
} LOGIN_EVENTS SEC(".maps");

// Counter for event keys
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 2);
    __type(key, __u32);
    __type(value, __u32);
} COUNTERS SEC(".maps");

static __always_inline int get_file_path(struct file *file, char *buf, size_t size) {
    if (!file || !buf || size == 0) {
        return -1;
    }
    
    struct dentry *dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry) {
        return -1;
    }
    
    // Get the dentry name with proper bounds checking
    const char *name = BPF_CORE_READ(dentry, d_name.name);
    if (!name) {
        return -1;
    }
    
    // Ensure we don't read beyond buffer size
    long ret = bpf_probe_read_kernel_str(buf, size, name);
    if (ret < 0) {
        return -1;
    }
    
    return 0;
}

static __always_inline void populate_file_event(struct file_event_data *event, 
                                               struct file *file, 
                                               __u32 event_type) {
    if (!event) {
        return;
    }
    
    // Initialize the event structure
    __builtin_memset(event, 0, sizeof(*event));
    
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    __u64 uid_gid = bpf_get_current_uid_gid();
    
    event->pid = pid_tgid >> 32;
    event->uid = uid_gid & 0xFFFFFFFF;
    event->gid = uid_gid >> 32;
    event->event_type = event_type;
    event->timestamp = bpf_ktime_get_ns();
    
    // Get current process command name
    long ret = bpf_get_current_comm(&event->comm, sizeof(event->comm));
    if (ret < 0) {
        bpf_probe_read_kernel_str(event->comm, sizeof(event->comm), "unknown");
    }
    
    // Get filename with error handling
    if (get_file_path(file, event->filename, sizeof(event->filename)) < 0) {
        bpf_probe_read_kernel_str(event->filename, sizeof(event->filename), "unknown");
    }
}

// File open monitoring
SEC("kprobe/do_sys_open")
int file_open(struct pt_regs *ctx) {
    struct file_event_data event = {};
    struct file *file = (struct file *)PT_REGS_RC(ctx);
    
    // Basic validation
    if (!file || !ctx) {
        return 0;
    }
    
    // Skip kernel threads
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    __u32 pid = pid_tgid >> 32;
    if (pid == 0) {
        return 0;
    }
    
    populate_file_event(&event, file, 0); // 0 = OPEN
    
    // Atomic counter increment and event storage
    __u32 counter_key = 0;
    __u32 *counter = bpf_map_lookup_elem(&COUNTERS, &counter_key);
    if (counter) {
        __u32 event_key = *counter;
        // Use NOEXIST to prevent race conditions
        long ret = bpf_map_update_elem(&FILE_EVENTS, &event_key, &event, BPF_NOEXIST);
        if (ret == 0) {
            __u32 new_counter = *counter + 1;
            bpf_map_update_elem(&COUNTERS, &counter_key, &new_counter, BPF_ANY);
        }
    }
    
    return 0;
}

// File write monitoring
SEC("kprobe/vfs_write")
int file_write(struct pt_regs *ctx) {
    struct file_event_data event = {};
    struct file *file = (struct file *)PT_REGS_PARM1(ctx);
    
    // Basic validation
    if (!file || !ctx) {
        return 0;
    }
    
    // Skip kernel threads
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    __u32 pid = pid_tgid >> 32;
    if (pid == 0) {
        return 0;
    }
    
    populate_file_event(&event, file, 1); // 1 = WRITE
    
    // Atomic counter increment and event storage
    __u32 counter_key = 0;
    __u32 *counter = bpf_map_lookup_elem(&COUNTERS, &counter_key);
    if (counter) {
        __u32 event_key = *counter;
        long ret = bpf_map_update_elem(&FILE_EVENTS, &event_key, &event, BPF_NOEXIST);
        if (ret == 0) {
            __u32 new_counter = *counter + 1;
            bpf_map_update_elem(&COUNTERS, &counter_key, &new_counter, BPF_ANY);
        }
    }
    
    return 0;
}

// File delete monitoring
SEC("kprobe/vfs_unlink")
int file_delete(struct pt_regs *ctx) {
    struct file_event_data event = {};
    struct inode *dir = (struct inode *)PT_REGS_PARM1(ctx);
    struct dentry *dentry = (struct dentry *)PT_REGS_PARM2(ctx);
    
    // Basic validation
    if (!dentry || !ctx) {
        return 0;
    }
    
    // Skip kernel threads
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    __u32 pid = pid_tgid >> 32;
    if (pid == 0) {
        return 0;
    }
    
    // Initialize the event structure
    __builtin_memset(&event, 0, sizeof(event));
    
    __u64 uid_gid = bpf_get_current_uid_gid();
    
    event.pid = pid;
    event.uid = uid_gid & 0xFFFFFFFF;
    event.gid = uid_gid >> 32;
    event.event_type = 2; // 2 = DELETE
    event.timestamp = bpf_ktime_get_ns();
    
    // Get current process command name
    long ret = bpf_get_current_comm(&event.comm, sizeof(event.comm));
    if (ret < 0) {
        bpf_probe_read_kernel_str(event.comm, sizeof(event.comm), "unknown");
    }
    
    // Get filename from dentry with proper bounds checking
    const char *name = BPF_CORE_READ(dentry, d_name.name);
    if (name) {
        ret = bpf_probe_read_kernel_str(event.filename, sizeof(event.filename), name);
        if (ret < 0) {
            bpf_probe_read_kernel_str(event.filename, sizeof(event.filename), "unknown");
        }
    } else {
        bpf_probe_read_kernel_str(event.filename, sizeof(event.filename), "unknown");
    }
    
    // Atomic counter increment and event storage
    __u32 counter_key = 0;
    __u32 *counter = bpf_map_lookup_elem(&COUNTERS, &counter_key);
    if (counter) {
        __u32 event_key = *counter;
        ret = bpf_map_update_elem(&FILE_EVENTS, &event_key, &event, BPF_NOEXIST);
        if (ret == 0) {
            __u32 new_counter = *counter + 1;
            bpf_map_update_elem(&COUNTERS, &counter_key, &new_counter, BPF_ANY);
        }
    }
    
    return 0;
}

// Login monitoring
SEC("tracepoint/syscalls/sys_enter_setuid")
int login_monitor(struct trace_event_raw_sys_enter *ctx) {
    struct login_event_data event = {};
    
    // Basic validation
    if (!ctx) {
        return 0;
    }
    
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    __u32 pid = pid_tgid >> 32;
    
    // Skip kernel threads
    if (pid == 0) {
        return 0;
    }
    
    // Initialize the event structure
    __builtin_memset(&event, 0, sizeof(event));
    
    __u64 uid_gid = bpf_get_current_uid_gid();
    
    event.pid = pid;
    event.uid = uid_gid & 0xFFFFFFFF;
    event.session_id = pid_tgid & 0xFFFFFFFF;
    event.event_type = 0; // 0 = LOGIN
    event.timestamp = bpf_ktime_get_ns();
    
    // Get username from current task with proper error handling
    struct task_struct *task = (struct task_struct *)bpf_get_current_task();
    if (task) {
        long ret = bpf_probe_read_kernel_str(event.username, sizeof(event.username), 
                                           BPF_CORE_READ(task, comm));
        if (ret < 0) {
            bpf_probe_read_kernel_str(event.username, sizeof(event.username), "unknown");
        }
    } else {
        bpf_probe_read_kernel_str(event.username, sizeof(event.username), "unknown");
    }
    
    // Atomic counter increment and event storage
    __u32 counter_key = 1;
    __u32 *counter = bpf_map_lookup_elem(&COUNTERS, &counter_key);
    if (counter) {
        __u32 event_key = *counter;
        long ret = bpf_map_update_elem(&LOGIN_EVENTS, &event_key, &event, BPF_NOEXIST);
        if (ret == 0) {
            __u32 new_counter = *counter + 1;
            bpf_map_update_elem(&COUNTERS, &counter_key, &new_counter, BPF_ANY);
        }
    }
    
    return 0;
}

char LICENSE[] SEC("license") = "GPL";
