use std::collections::HashMap;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH, Duration};

use anyhow::{Context, Result};
use aya::maps::HashMap as AyaHashMap;
use aya::programs::{TracePoint, KProbe};
use aya::{include_bytes_aligned, Bpf};
use aya_log::BpfLogger;
use clap::Parser;
use log::{info, warn, debug, error};
use tokio::signal;
use tokio::sync::Mutex;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileEvent {
    pub timestamp: DateTime<Utc>,
    pub pid: u32,
    pub uid: u32,
    pub gid: u32,
    pub comm: String,
    pub filename: String,
    pub event_type: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginEvent {
    pub timestamp: DateTime<Utc>,
    pub pid: u32,
    pub uid: u32,
    pub username: String,
    pub session_id: u32,
    pub event_type: String,
}

#[derive(Debug, Parser)]
#[command(name = "file-monitor-ebpf")]
#[command(about = "eBPF-based file monitoring and login tracking system")]
struct Opt {
    #[arg(short, long, help = "Enable verbose output with JSON logging")]
    verbose: bool,

    #[arg(short, long, help = "Filter events by file extension (e.g., 'txt,log,conf')")]
    filter: Option<String>,

    #[arg(short, long, help = "Output format: text or json", default_value = "text")]
    output: String,

    #[arg(long, help = "Minimum UID to monitor (skip system users)", default_value = "1000")]
    min_uid: u32,
}

#[repr(C)]
#[derive(Debug, Clone, Copy)]
struct FileEventData {
    pid: u32,
    uid: u32,
    gid: u32,
    comm: [u8; 16],
    filename: [u8; 256],
    event_type: u32,
    timestamp: u64,
}

#[repr(C)]
#[derive(Debug, Clone, Copy)]
struct LoginEventData {
    pid: u32,
    uid: u32,
    username: [u8; 32],
    session_id: u32,
    event_type: u32,
    timestamp: u64,
}

unsafe impl aya::Pod for FileEventData {}
unsafe impl aya::Pod for LoginEventData {}

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();
    let opt = Opt::parse();

    info!("🚀 eBPF File Monitor starting...");

    // Check if running as root
    if unsafe { libc::geteuid() } != 0 {
        error!("❌ This application requires root privileges to load eBPF programs");
        error!("   Please run with: sudo ./file-monitor-ebpf");
        return Ok(());
    }

    // Load the eBPF program
    info!("📦 Loading eBPF program...");

    #[cfg(debug_assertions)]
    let mut bpf = Bpf::load(include_bytes_aligned!(
        "../../target/bpfel-unknown-none/debug/file-monitor-ebpf"
    ))?;
    #[cfg(not(debug_assertions))]
    let mut bpf = Bpf::load(include_bytes_aligned!(
        "../../target/bpfel-unknown-none/release/file-monitor-ebpf"
    ))?;

    if let Err(e) = BpfLogger::init(&mut bpf) {
        warn!("Failed to initialize eBPF logger: {}", e);
    }

    // File monitoring - attach to file operation syscalls
    info!("🔗 Attaching to kernel hooks...");

    let file_open_prog: &mut KProbe = bpf.program_mut("file_open").unwrap().try_into()?;
    file_open_prog.load()?;
    file_open_prog.attach("do_sys_open", 0)
        .context("Failed to attach to do_sys_open")?;

    let file_write_prog: &mut KProbe = bpf.program_mut("file_write").unwrap().try_into()?;
    file_write_prog.load()?;
    file_write_prog.attach("vfs_write", 0)
        .context("Failed to attach to vfs_write")?;

    let file_delete_prog: &mut KProbe = bpf.program_mut("file_delete").unwrap().try_into()?;
    file_delete_prog.load()?;
    file_delete_prog.attach("vfs_unlink", 0)
        .context("Failed to attach to vfs_unlink")?;

    // Login monitoring - attach to login-related tracepoints
    let login_prog: &mut TracePoint = bpf.program_mut("login_monitor").unwrap().try_into()?;
    login_prog.load()?;
    login_prog.attach("syscalls", "sys_enter_setuid")
        .context("Failed to attach to setuid tracepoint")?;

    // Get references to the maps
    let mut file_events: AyaHashMap<_, u32, FileEventData> =
        AyaHashMap::try_from(bpf.map_mut("FILE_EVENTS").unwrap())?;
    let mut login_events: AyaHashMap<_, u32, LoginEventData> =
        AyaHashMap::try_from(bpf.map_mut("LOGIN_EVENTS").unwrap())?;

    info!("✅ eBPF program loaded successfully");
    info!("🔍 Monitoring file operations and login events...");
    info!("📊 Output format: {}", opt.output);

    if let Some(filter) = &opt.filter {
        info!("🔧 File filter: {}", filter);
    }

    info!("👥 Monitoring UIDs >= {}", opt.min_uid);
    info!("🛑 Press Ctrl+C to stop monitoring");

    // Event processing loop
    let event_processor = Arc::new(Mutex::new(EventProcessor::new(opt)));
    let processor_clone = event_processor.clone();

    let processing_task = tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_millis(100));
        loop {
            interval.tick().await;

            let mut processor = processor_clone.lock().await;
            if let Err(e) = processor.process_events(&mut file_events, &mut login_events).await {
                error!("Error processing events: {}", e);
            }
        }
    });

    // Wait for Ctrl-C
    tokio::select! {
        _ = signal::ctrl_c() => {
            info!("🛑 Received Ctrl+C, shutting down...");
        }
        _ = processing_task => {
            warn!("Event processing task ended unexpectedly");
        }
    }

    info!("👋 File monitor stopped");
    Ok(())
}

struct EventProcessor {
    file_event_counter: u32,
    login_event_counter: u32,
    opt: Opt,
}

impl EventProcessor {
    fn new(opt: Opt) -> Self {
        Self {
            file_event_counter: 0,
            login_event_counter: 0,
            opt,
        }
    }

    async fn process_events(
        &mut self,
        file_events: &mut AyaHashMap<_, u32, FileEventData>,
        login_events: &mut AyaHashMap<_, u32, LoginEventData>,
    ) -> Result<()> {
        // Process file events
        for key in file_events.keys() {
            if let Ok(key) = key {
                if let Ok(Some(event_data)) = file_events.get(&key, 0) {
                    let event = self.convert_file_event(event_data);

                    // Apply filters
                    if self.should_process_file_event(&event) {
                        self.handle_file_event(event).await?;
                    }

                    let _ = file_events.remove(&key);
                }
            }
        }

        // Process login events
        for key in login_events.keys() {
            if let Ok(key) = key {
                if let Ok(Some(event_data)) = login_events.get(&key, 0) {
                    let event = self.convert_login_event(event_data);

                    if self.should_process_login_event(&event) {
                        self.handle_login_event(event).await?;
                    }

                    let _ = login_events.remove(&key);
                }
            }
        }

        Ok(())
    }

    fn should_process_file_event(&self, event: &FileEvent) -> bool {
        // Skip system users if min_uid is set
        if event.uid < self.opt.min_uid {
            return false;
        }

        // Apply file extension filter
        if let Some(filter) = &self.opt.filter {
            let extensions: Vec<&str> = filter.split(',').collect();
            let file_extension = event.filename.split('.').last().unwrap_or("");

            if !extensions.iter().any(|&ext| ext == file_extension) {
                return false;
            }
        }

        true
    }

    fn should_process_login_event(&self, event: &LoginEvent) -> bool {
        // Skip system users if min_uid is set
        event.uid >= self.opt.min_uid
    }

    fn convert_file_event(&self, data: FileEventData) -> FileEvent {
        let comm = String::from_utf8_lossy(&data.comm)
            .trim_end_matches('\0')
            .to_string();
        let filename = String::from_utf8_lossy(&data.filename)
            .trim_end_matches('\0')
            .to_string();

        let event_type = match data.event_type {
            0 => "OPEN",
            1 => "WRITE",
            2 => "DELETE",
            _ => "UNKNOWN",
        };

        let timestamp = SystemTime::UNIX_EPOCH +
            Duration::from_nanos(data.timestamp);
        let datetime = DateTime::<Utc>::from(timestamp);

        FileEvent {
            timestamp: datetime,
            pid: data.pid,
            uid: data.uid,
            gid: data.gid,
            comm,
            filename,
            event_type: event_type.to_string(),
        }
    }

    fn convert_login_event(&self, data: LoginEventData) -> LoginEvent {
        let username = String::from_utf8_lossy(&data.username)
            .trim_end_matches('\0')
            .to_string();

        let timestamp = SystemTime::UNIX_EPOCH +
            Duration::from_nanos(data.timestamp);
        let datetime = DateTime::<Utc>::from(timestamp);

        LoginEvent {
            timestamp: datetime,
            pid: data.pid,
            uid: data.uid,
            username,
            session_id: data.session_id,
            event_type: "LOGIN".to_string(),
        }
    }

    async fn handle_file_event(&mut self, event: FileEvent) -> Result<()> {
        self.file_event_counter += 1;

        if self.opt.output == "json" {
            println!("{}", serde_json::to_string(&event)?);
        } else {
            // Get user information
            let username = get_username_from_uid(event.uid).unwrap_or_else(|| event.uid.to_string());

            println!("[FILE EVENT #{}] {} - {} by {} (PID: {}, UID: {}, GID: {}): {}",
                self.file_event_counter,
                event.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
                event.event_type,
                username,
                event.pid,
                event.uid,
                event.gid,
                event.filename
            );

            // Log to JSON for structured processing
            if self.opt.verbose {
                if let Ok(json) = serde_json::to_string_pretty(&event) {
                    println!("  JSON: {}", json);
                }
            }
        }

        Ok(())
    }

    async fn handle_login_event(&mut self, event: LoginEvent) -> Result<()> {
        self.login_event_counter += 1;

        if self.opt.output == "json" {
            println!("{}", serde_json::to_string(&event)?);
        } else {
            println!("[LOGIN EVENT #{}] {} - User '{}' logged in (PID: {}, UID: {}, Session: {})",
                self.login_event_counter,
                event.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
                event.username,
                event.pid,
                event.uid,
                event.session_id
            );

            // Log to JSON for structured processing
            if self.opt.verbose {
                if let Ok(json) = serde_json::to_string_pretty(&event) {
                    println!("  JSON: {}", json);
                }
            }
        }

        Ok(())
    }
}

fn get_username_from_uid(uid: u32) -> Option<String> {
    use std::process::Command;

    let output = Command::new("id")
        .arg("-nu")
        .arg(uid.to_string())
        .output()
        .ok()?;

    if output.status.success() {
        Some(String::from_utf8_lossy(&output.stdout).trim().to_string())
    } else {
        None
    }
}
