use std::collections::HashMap;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH, Duration};
use std::path::Path;

use anyhow::{Context, Result, anyhow};
use aya::maps::HashMap as AyaHashMap;
use aya::programs::{TracePoint, KProbe};
use aya::{include_bytes_aligned, Bpf};
use aya_log::BpfLogger;
use clap::Parser;
use log::{info, warn, debug, error};
use tokio::signal;
use tokio::sync::Mutex;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FileEvent {
    pub timestamp: DateTime<Utc>,
    pub pid: u32,
    pub uid: u32,
    pub gid: u32,
    pub comm: String,
    pub filename: String,
    pub event_type: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginEvent {
    pub timestamp: DateTime<Utc>,
    pub pid: u32,
    pub uid: u32,
    pub username: String,
    pub session_id: u32,
    pub event_type: String,
}

#[derive(Debug, Parser)]
#[command(name = "file-monitor-ebpf")]
#[command(about = "eBPF-based file monitoring and login tracking system")]
struct Opt {
    #[arg(short, long, help = "Enable verbose output with JSON logging")]
    verbose: bool,

    #[arg(short, long, help = "Filter events by file extension (e.g., 'txt,log,conf')")]
    filter: Option<String>,

    #[arg(short, long, help = "Output format: text or json", default_value = "text")]
    output: String,

    #[arg(long, help = "Maximum number of events to process per batch", default_value = "100")]
    batch_size: usize,

    #[arg(long, help = "Event processing interval in milliseconds", default_value = "100")]
    interval_ms: u64,

    #[arg(long, help = "Minimum UID to monitor (skip system users)", default_value = "1000")]
    min_uid: u32,
}

#[repr(C)]
#[derive(Debug, Clone, Copy)]
struct FileEventData {
    pid: u32,
    uid: u32,
    gid: u32,
    comm: [u8; 16],
    filename: [u8; 256],
    event_type: u32,
    timestamp: u64,
}

#[repr(C)]
#[derive(Debug, Clone, Copy)]
struct LoginEventData {
    pid: u32,
    uid: u32,
    username: [u8; 32],
    session_id: u32,
    event_type: u32,
    timestamp: u64,
}

unsafe impl aya::Pod for FileEventData {}
unsafe impl aya::Pod for LoginEventData {}

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::init();
    let opt = Opt::parse();

    // Validate configuration
    if let Err(e) = validate_config(&opt) {
        error!("Configuration validation failed: {}", e);
        return Err(e);
    }

    info!("🚀 eBPF File Monitor starting...");

    // Check if running as root
    if unsafe { libc::geteuid() } != 0 {
        error!("❌ This application requires root privileges to load eBPF programs");
        error!("   Please run with: sudo ./file-monitor-ebpf");
        return Ok(());
    }

    // Load the eBPF program
    info!("📦 Loading eBPF program...");

    #[cfg(debug_assertions)]
    let mut bpf = Bpf::load(include_bytes_aligned!(
        "../../target/debug/file_monitor.bpf.o"
    ))?;
    #[cfg(not(debug_assertions))]
    let mut bpf = Bpf::load(include_bytes_aligned!(
        "../../target/release/file_monitor.bpf.o"
    ))?;

    if let Err(e) = BpfLogger::init(&mut bpf) {
        warn!("Failed to initialize eBPF logger: {}", e);
    }

    // File monitoring - attach to file operation syscalls
    info!("🔗 Attaching to kernel hooks...");

    let file_open_prog: &mut KProbe = bpf.program_mut("file_open").unwrap().try_into()?;
    file_open_prog.load()?;
    file_open_prog.attach("do_sys_open", 0)
        .context("Failed to attach to do_sys_open")?;

    let file_write_prog: &mut KProbe = bpf.program_mut("file_write").unwrap().try_into()?;
    file_write_prog.load()?;
    file_write_prog.attach("vfs_write", 0)
        .context("Failed to attach to vfs_write")?;

    let file_delete_prog: &mut KProbe = bpf.program_mut("file_delete").unwrap().try_into()?;
    file_delete_prog.load()?;
    file_delete_prog.attach("vfs_unlink", 0)
        .context("Failed to attach to vfs_unlink")?;

    // Login monitoring - attach to login-related tracepoints
    let login_prog: &mut TracePoint = bpf.program_mut("login_monitor").unwrap().try_into()?;
    login_prog.load()?;
    login_prog.attach("syscalls", "sys_enter_setuid")
        .context("Failed to attach to setuid tracepoint")?;

    // Get references to the maps
    let mut file_events: AyaHashMap<_, u32, FileEventData> =
        AyaHashMap::try_from(bpf.map_mut("FILE_EVENTS").unwrap())?;
    let mut login_events: AyaHashMap<_, u32, LoginEventData> =
        AyaHashMap::try_from(bpf.map_mut("LOGIN_EVENTS").unwrap())?;

    info!("✅ eBPF program loaded successfully");
    info!("🔍 Monitoring file operations and login events...");
    info!("📊 Output format: {}", opt.output);
    info!("⚙️  Batch size: {}, Interval: {}ms", opt.batch_size, opt.interval_ms);

    if let Some(filter) = &opt.filter {
        info!("🔧 File filter: {}", filter);
    }

    info!("👥 Monitoring UIDs >= {}", opt.min_uid);
    info!("🛑 Press Ctrl+C to stop monitoring");

    // Event processing loop
    let event_processor = Arc::new(Mutex::new(EventProcessor::new(opt)));
    let processor_clone = event_processor.clone();

    let processing_task = tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_millis(processor_clone.lock().await.opt.interval_ms));
        let mut consecutive_errors = 0;
        const MAX_CONSECUTIVE_ERRORS: u32 = 5;
        
        loop {
            interval.tick().await;

            let mut processor = processor_clone.lock().await;
            match processor.process_events(&mut file_events, &mut login_events).await {
                Ok(_) => {
                    consecutive_errors = 0;
                },
                Err(e) => {
                    consecutive_errors += 1;
                    error!("Error processing events: {} (consecutive errors: {})", e, consecutive_errors);
                    
                    if consecutive_errors >= MAX_CONSECUTIVE_ERRORS {
                        error!("Too many consecutive errors, stopping event processing");
                        break;
                    }
                    
                    // Brief backoff on errors
                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
            }
        }
    });

    // Wait for Ctrl-C
    tokio::select! {
        _ = signal::ctrl_c() => {
            info!("🛑 Received Ctrl+C, shutting down...");
        }
        _ = processing_task => {
            warn!("Event processing task ended unexpectedly");
        }
    }

    info!("👋 File monitor stopped");
    Ok(())
}

struct EventProcessor {
    file_event_counter: u32,
    login_event_counter: u32,
    opt: Opt,
    username_cache: HashMap<u32, String>,
}

impl EventProcessor {
    fn new(opt: Opt) -> Self {
        Self {
            file_event_counter: 0,
            login_event_counter: 0,
            opt,
            username_cache: HashMap::new(),
        }
    }

    fn get_cached_username(&mut self, uid: u32) -> String {
        // Check cache first
        if let Some(username) = self.username_cache.get(&uid) {
            return username.clone();
        }

        // Cache miss, look up username
        let username = get_username_from_uid(uid).unwrap_or_else(|| uid.to_string());
        
        // Cache the result (with a reasonable cache size limit)
        if self.username_cache.len() < 1000 {
            self.username_cache.insert(uid, username.clone());
        }
        
        username
    }

    async fn process_events(
        &mut self,
        file_events: &mut AyaHashMap<_, u32, FileEventData>,
        login_events: &mut AyaHashMap<_, u32, LoginEventData>,
    ) -> Result<()> {
        let mut processed = 0;
        let max_batch = self.opt.batch_size;
        
        // Process file events with batching
        let file_keys: Vec<_> = file_events.keys().collect();
        for key_result in file_keys.into_iter().take(max_batch) {
            if processed >= max_batch {
                break;
            }
            
            let key = key_result.map_err(|e| anyhow!("Failed to get file event key: {}", e))?;
            
            match file_events.get(&key, 0) {
                Ok(Some(event_data)) => {
                    let event = self.convert_file_event(event_data);

                    if self.should_process_file_event(&event) {
                        if let Err(e) = self.handle_file_event(event).await {
                            warn!("Failed to handle file event: {}", e);
                        }
                    }

                    if let Err(e) = file_events.remove(&key) {
                        debug!("Failed to remove file event from map: {}", e);
                    }
                    processed += 1;
                },
                Ok(None) => {
                    // Event already processed or doesn't exist
                    if let Err(e) = file_events.remove(&key) {
                        debug!("Failed to remove missing file event: {}", e);
                    }
                },
                Err(e) => {
                    warn!("Failed to get file event data: {}", e);
                }
            }
        }

        // Process login events with batching
        let login_keys: Vec<_> = login_events.keys().collect();
        for key_result in login_keys.into_iter().take(max_batch - processed) {
            if processed >= max_batch {
                break;
            }
            
            let key = key_result.map_err(|e| anyhow!("Failed to get login event key: {}", e))?;
            
            match login_events.get(&key, 0) {
                Ok(Some(event_data)) => {
                    let event = self.convert_login_event(event_data);

                    if self.should_process_login_event(&event) {
                        if let Err(e) = self.handle_login_event(event).await {
                            warn!("Failed to handle login event: {}", e);
                        }
                    }

                    if let Err(e) = login_events.remove(&key) {
                        debug!("Failed to remove login event from map: {}", e);
                    }
                    processed += 1;
                },
                Ok(None) => {
                    if let Err(e) = login_events.remove(&key) {
                        debug!("Failed to remove missing login event: {}", e);
                    }
                },
                Err(e) => {
                    warn!("Failed to get login event data: {}", e);
                }
            }
        }

        if processed > 0 {
            debug!("Processed {} events in this batch", processed);
        }

        Ok(())
    }

    fn should_process_file_event(&self, event: &FileEvent) -> bool {
        // Skip system users if min_uid is set
        if event.uid < self.opt.min_uid {
            return false;
        }

        // Skip empty or invalid filenames
        if event.filename.is_empty() || event.filename == "unknown" {
            return false;
        }

        // Apply file extension filter
        if let Some(filter) = &self.opt.filter {
            let extensions: Vec<&str> = filter.split(',').map(|s| s.trim()).collect();
            let file_extension = Path::new(&event.filename)
                .extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("");

            if !extensions.iter().any(|&ext| ext == file_extension) {
                return false;
            }
        }

        true
    }

    fn should_process_login_event(&self, event: &LoginEvent) -> bool {
        // Skip system users if min_uid is set
        event.uid >= self.opt.min_uid
    }

    fn convert_file_event(&self, data: FileEventData) -> FileEvent {
        let comm = String::from_utf8_lossy(&data.comm)
            .trim_end_matches('\0')
            .trim()
            .to_string();
        let filename = String::from_utf8_lossy(&data.filename)
            .trim_end_matches('\0')
            .trim()
            .to_string();

        let event_type = match data.event_type {
            0 => "OPEN",
            1 => "WRITE",
            2 => "DELETE",
            _ => "UNKNOWN",
        };

        // Handle potential timestamp overflow
        let timestamp = if data.timestamp > 0 {
            SystemTime::UNIX_EPOCH + Duration::from_nanos(data.timestamp)
        } else {
            SystemTime::now()
        };
        let datetime = DateTime::<Utc>::from(timestamp);

        FileEvent {
            timestamp: datetime,
            pid: data.pid,
            uid: data.uid,
            gid: data.gid,
            comm,
            filename,
            event_type: event_type.to_string(),
        }
    }

    fn convert_login_event(&self, data: LoginEventData) -> LoginEvent {
        let username = String::from_utf8_lossy(&data.username)
            .trim_end_matches('\0')
            .trim()
            .to_string();

        // Handle potential timestamp overflow
        let timestamp = if data.timestamp > 0 {
            SystemTime::UNIX_EPOCH + Duration::from_nanos(data.timestamp)
        } else {
            SystemTime::now()
        };
        let datetime = DateTime::<Utc>::from(timestamp);

        LoginEvent {
            timestamp: datetime,
            pid: data.pid,
            uid: data.uid,
            username,
            session_id: data.session_id,
            event_type: "LOGIN".to_string(),
        }
    }

    async fn handle_file_event(&mut self, event: FileEvent) -> Result<()> {
        self.file_event_counter += 1;

        if self.opt.output == "json" {
            match serde_json::to_string(&event) {
                Ok(json) => println!("{}", json),
                Err(e) => {
                    warn!("Failed to serialize file event to JSON: {}", e);
                    return Ok(());
                }
            }
        } else {
            // Get user information with caching to avoid repeated syscalls
            let username = self.get_cached_username(event.uid);

            println!("[FILE EVENT #{}] {} - {} by {} (PID: {}, UID: {}, GID: {}): {}",
                self.file_event_counter,
                event.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
                event.event_type,
                username,
                event.pid,
                event.uid,
                event.gid,
                event.filename
            );

            // Log to JSON for structured processing
            if self.opt.verbose {
                match serde_json::to_string_pretty(&event) {
                    Ok(json) => println!("  JSON: {}", json),
                    Err(e) => debug!("Failed to serialize file event to pretty JSON: {}", e),
                }
            }
        }

        Ok(())
    }

    async fn handle_login_event(&mut self, event: LoginEvent) -> Result<()> {
        self.login_event_counter += 1;

        if self.opt.output == "json" {
            match serde_json::to_string(&event) {
                Ok(json) => println!("{}", json),
                Err(e) => {
                    warn!("Failed to serialize login event to JSON: {}", e);
                    return Ok(());
                }
            }
        } else {
            println!("[LOGIN EVENT #{}] {} - User '{}' logged in (PID: {}, UID: {}, Session: {})",
                self.login_event_counter,
                event.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
                event.username,
                event.pid,
                event.uid,
                event.session_id
            );

            // Log to JSON for structured processing
            if self.opt.verbose {
                match serde_json::to_string_pretty(&event) {
                    Ok(json) => println!("  JSON: {}", json),
                    Err(e) => debug!("Failed to serialize login event to pretty JSON: {}", e),
                }
            }
        }

        Ok(())
    }
}

fn validate_config(opt: &Opt) -> Result<()> {
    // Validate output format
    if opt.output != "text" && opt.output != "json" {
        return Err(anyhow!("Invalid output format: {}. Must be 'text' or 'json'", opt.output));
    }

    // Validate batch size
    if opt.batch_size == 0 || opt.batch_size > 10000 {
        return Err(anyhow!("Invalid batch size: {}. Must be between 1 and 10000", opt.batch_size));
    }

    // Validate interval
    if opt.interval_ms == 0 || opt.interval_ms > 60000 {
        return Err(anyhow!("Invalid interval: {}ms. Must be between 1 and 60000", opt.interval_ms));
    }

    // Validate filter format
    if let Some(filter) = &opt.filter {
        if filter.trim().is_empty() {
            return Err(anyhow!("Filter cannot be empty"));
        }
        
        // Check for invalid characters in extensions
        for ext in filter.split(',') {
            let ext = ext.trim();
            if ext.is_empty() || ext.contains('/') || ext.contains('\\') {
                return Err(anyhow!("Invalid file extension in filter: '{}'", ext));
            }
        }
    }

    Ok(())
}

fn get_username_from_uid(uid: u32) -> Option<String> {
    use std::process::Command;
    use std::time::Duration;

    let output = Command::new("timeout")
        .arg("2s")  // 2 second timeout
        .arg("id")
        .arg("-nu")
        .arg(uid.to_string())
        .output()
        .ok()?;

    if output.status.success() {
        let username = String::from_utf8_lossy(&output.stdout).trim().to_string();
        if !username.is_empty() && username.len() <= 32 {
            Some(username)
        } else {
            None
        }
    } else {
        None
    }
}
