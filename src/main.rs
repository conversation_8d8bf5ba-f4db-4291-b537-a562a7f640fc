use std::time::Duration;
use clap::Parser;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use tokio::signal;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileEvent {
    pub timestamp: DateTime<Utc>,
    pub pid: u32,
    pub uid: u32,
    pub gid: u32,
    pub comm: String,
    pub filename: String,
    pub event_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginEvent {
    pub timestamp: DateTime<Utc>,
    pub pid: u32,
    pub uid: u32,
    pub username: String,
    pub session_id: u32,
    pub event_type: String,
}

#[derive(Debug, Parser)]
struct Opt {
    #[clap(short, long)]
    demo: bool,
    #[clap(short, long)]
    verbose: bool,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let opt = Opt::parse();
    
    println!("🚀 eBPF File Monitor Starting...");
    println!("===================================\n");
    
    if opt.demo {
        println!("Running in DEMO mode - simulating file monitoring events");
        println!("In production, this would use real eBPF programs to monitor kernel events\n");
        
        // Simulate some file monitoring events
        let demo_events = vec![
            ("FILE", "WRITE", "alice", 1234, 1000, 1000, "/home/<USER>/document.txt"),
            ("LOGIN", "LOGIN", "charlie", 1267, 1002, 1002, ""),
            ("FILE", "OPEN", "root", 1280, 0, 0, "/etc/passwd"),
            ("FILE", "DELETE", "bob", 1256, 1001, 1001, "/tmp/tempfile.log"),
            ("FILE", "WRITE", "alice", 1234, 1000, 1000, "/home/<USER>/config.json"),
            ("LOGIN", "LOGIN", "david", 1300, 1003, 1003, ""),
            ("FILE", "OPEN", "alice", 1234, 1000, 1000, "/var/log/app.log"),
        ];
        
        let mut event_counter = 0;
        
        for (event_type, action, username, pid, uid, gid, filename) in demo_events {
            tokio::time::sleep(Duration::from_secs(2)).await;
            
            event_counter += 1;
            let now = Utc::now();
            
            if event_type == "FILE" {
                println!("[FILE EVENT #{}] {} - {} by {} (PID: {}, UID: {}, GID: {}): {}", 
                    event_counter,
                    now.format("%Y-%m-%d %H:%M:%S UTC"),
                    action,
                    username,
                    pid,
                    uid,
                    gid,
                    filename
                );
                
                if opt.verbose {
                    let file_event = FileEvent {
                        timestamp: now,
                        pid,
                        uid,
                        gid,
                        comm: format!("{}cmd", username),
                        filename: filename.to_string(),
                        event_type: action.to_string(),
                    };
                    
                    if let Ok(json) = serde_json::to_string_pretty(&file_event) {
                        println!("  JSON: {}", json);
                    }
                }
            } else if event_type == "LOGIN" {
                println!("[LOGIN EVENT #{}] {} - User '{}' logged in (PID: {}, UID: {}, Session: {})", 
                    event_counter,
                    now.format("%Y-%m-%d %H:%M:%S UTC"),
                    username,
                    pid,
                    uid,
                    pid
                );
                
                if opt.verbose {
                    let login_event = LoginEvent {
                        timestamp: now,
                        pid,
                        uid,
                        username: username.to_string(),
                        session_id: pid,
                        event_type: "LOGIN".to_string(),
                    };
                    
                    if let Ok(json) = serde_json::to_string_pretty(&login_event) {
                        println!("  JSON: {}", json);
                    }
                }
            }
            
            // Check if we should exit
            if let Ok(_) = tokio::time::timeout(Duration::from_millis(100), signal::ctrl_c()).await {
                println!("\n🛑 Received Ctrl+C, shutting down...");
                break;
            }
        }
        
        println!("\n✅ Demo completed successfully!");
        println!("In a real Linux environment with root privileges, this would:");
        println!("  • Load actual eBPF programs into the kernel");
        println!("  • Monitor real file operations (open, write, delete)");
        println!("  • Track actual user login events");
        println!("  • Provide real-time system monitoring");
        
    } else {
        println!("⚠️  Production mode requires:");
        println!("  • Linux kernel 4.18+ with eBPF support");
        println!("  • Root privileges to load eBPF programs");
        println!("  • Privileged container or bare metal deployment");
        println!();
        println!("Use --demo flag to see simulated events");
        println!("Example: ./file-monitor-ebpf --demo --verbose");
    }
    
    Ok(())
}
