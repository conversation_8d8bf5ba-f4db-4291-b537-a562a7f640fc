# Dockerfile for eBPF File Monitor

FROM ubuntu:22.04

# Avoid prompts from apt
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    clang \
    llvm \
    libbpf-dev \
    linux-headers-generic \
    pkg-config \
    libssl-dev \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Rust
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
ENV PATH="/root/.cargo/bin:${PATH}"

# Note: For production eBPF, you would add:
# RUN rustup target add bpfel-unknown-none && \
#     cargo install bpf-linker

# Set working directory
WORKDIR /app

# Copy source code
COPY . .

# Build the application
RUN cargo build --release

# Create a non-root user (though eBPF requires root to run)
RUN useradd -m -u 1000 monitor

# Set proper permissions
RUN chown -R monitor:monitor /app

# Default command
CMD ["./target/release/file-monitor-ebpf"]

# Metadata
LABEL org.opencontainers.image.title="eBPF File Monitor"
LABEL org.opencontainers.image.description="A file monitoring application using eBPF in Rust"
LABEL org.opencontainers.image.version="0.1.0"
LABEL org.opencontainers.image.authors="System Monitor Developer"
