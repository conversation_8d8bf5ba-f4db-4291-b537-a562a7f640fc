use std::env;
use std::path::PathBuf;

fn main() {
    let target_os = env::var("CARGO_CFG_TARGET_OS").unwrap();

    // Only compile eBPF program on Linux
    if target_os != "linux" {
        println!("cargo:warning=eBPF compilation skipped on non-Linux platform");
        return;
    }

    let out_dir = PathBuf::from(env::var_os("OUT_DIR").unwrap());

    println!("cargo:rerun-if-changed=src/file_monitor.bpf.c");

    // Check if eBPF source file exists
    if !std::path::Path::new("src/file_monitor.bpf.c").exists() {
        println!("cargo:warning=eBPF source file not found, skipping compilation");
        return;
    }

    // Compile eBPF program
    let status = std::process::Command::new("clang")
        .args([
            "-target", "bpf",
            "-O2", "-g",
            "-c", "src/file_monitor.bpf.c",
            "-o", &format!("{}/file_monitor.bpf.o", out_dir.display())
        ])
        .status()
        .expect("Failed to compile eBPF program");

    if !status.success() {
        panic!("Failed to compile eBPF program");
    }
}
