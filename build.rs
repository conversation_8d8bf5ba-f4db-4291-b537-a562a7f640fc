use std::env;
use std::path::PathBuf;

fn main() {
    let target_os = env::var("CARGO_CFG_TARGET_OS").unwrap();

    // Only compile eBPF program on Linux
    if target_os != "linux" {
        println!("cargo:warning=eBPF compilation skipped on non-Linux platform");
        return;
    }

    println!("cargo:rerun-if-changed=src/file_monitor.bpf.c");

    // Check if eBPF source file exists
    if !std::path::Path::new("src/file_monitor.bpf.c").exists() {
        println!("cargo:warning=eBPF source file not found, skipping compilation");
        return;
    }

    // Compile eBPF program directly to target directory
    let target_dir = env::var("CARGO_TARGET_DIR").unwrap_or_else(|_| "target".to_string());
    let profile = env::var("PROFILE").unwrap_or_else(|_| "debug".to_string());
    let output_path = format!("{}/{}/file_monitor.bpf.o", target_dir, profile);

    // Ensure target directory exists
    std::fs::create_dir_all(format!("{}/{}", target_dir, profile)).ok();

    // Compile eBPF program
    let status = std::process::Command::new("clang")
        .args([
            "-target", "bpf",
            "-O2", "-g",
            "-I/usr/include",
            "-I/usr/include/x86_64-linux-gnu",
            "-c", "src/file_monitor.bpf.c",
            "-o", &output_path
        ])
        .status()
        .expect("Failed to compile eBPF program");

    if !status.success() {
        panic!("Failed to compile eBPF program");
    }

    println!("cargo:warning=eBPF program compiled to {}", output_path);
}
