[package]
name = "file-monitor-ebpf"
version = "0.1.0"
edition = "2021"

[dependencies]
clap = { version = "4.0", features = ["derive"] }
tokio = { version = "1.0", features = ["macros", "rt", "rt-multi-thread", "net", "signal", "time"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }

# For production eBPF version (commented out for demo)
# aya = { version = "0.12", features = ["async_tokio"] }
# aya-log = "0.2"
# env_logger = "0.10"
# log = "0.4"
# anyhow = "1.0"
# bytes = "1.4"

# [build-dependencies]
# aya-tool = "0.12"

[[bin]]
name = "file-monitor-ebpf"
path = "src/main.rs"
