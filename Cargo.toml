[package]
name = "file-monitor-ebpf"
version = "0.1.0"
edition = "2021"

[dependencies]
aya = { version = "0.12", features = ["async_tokio"] }
aya-log = "0.2"
clap = { version = "4.0", features = ["derive"] }
env_logger = "0.10"
log = "0.4"
tokio = { version = "1.0", features = ["macros", "rt", "rt-multi-thread", "net", "signal", "time"] }
anyhow = "1.0"
bytes = "1.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
libc = "0.2"



[[bin]]
name = "file-monitor-ebpf"
path = "src/main.rs"
