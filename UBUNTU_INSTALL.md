# 🐧 eBPF File Monitor - Ubuntu Installation Guide

This guide walks you through setting up and running the eBPF file monitoring application on Ubuntu.

## 📋 Prerequisites

### System Requirements
- **Ubuntu 18.04 LTS or newer**
- **Linux kernel 4.18 or higher**
- **Root privileges** (for loading eBPF programs)
- **At least 2GB RAM** (for development)
- **Internet connection** (for downloading dependencies)

### Quick Requirements Check
```bash
# Check Ubuntu version
lsb_release -a

# Check kernel version
uname -r

# Check if you have sudo access
sudo echo "OK"
```

## 🚀 Quick Start (Automated Setup)

### Option 1: One-Command Setup
```bash
# Download and run the setup script
curl -sSL https://raw.githubusercontent.com/your-repo/file-monitor-ebpf/main/ubuntu_setup.sh | bash

# Or if you have the project files:
chmod +x ubuntu_setup.sh
./ubuntu_setup.sh
```

## 🔧 Manual Installation

### Step 1: Update System
```bash
sudo apt update && sudo apt upgrade -y
```

### Step 2: Install Required Packages
```bash
sudo apt install -y \
    build-essential \
    clang \
    llvm \
    libbpf-dev \
    linux-headers-$(uname -r) \
    pkg-config \
    libssl-dev \
    curl \
    git \
    linux-tools-$(uname -r) \
    linux-tools-common \
    linux-tools-generic
```

### Step 3: Install Rust
```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y

# Load Rust environment
source ~/.cargo/env

# Add eBPF target
rustup target add bpfel-unknown-none

# Install BPF linker
cargo install bpf-linker
```

### Step 4: Verify eBPF Support
```bash
# Check if eBPF is supported
ls /proc/sys/kernel/unprivileged_bpf_disabled

# Check if bpftool is available
which bpftool
```

## 📁 Project Setup

### Step 1: Create Project Directory
```bash
mkdir -p ~/file-monitor-ebpf
cd ~/file-monitor-ebpf
```

### Step 2: Download Project Files
```bash
# If you have the project files, copy them
# Otherwise, create the basic structure:

# Create source directory
mkdir -p src

# Copy or create the necessary files:
# - src/main.rs
# - src/file_monitor.bpf.c
# - Cargo.toml
# - build.rs
```

### Step 3: Update Cargo.toml for Production
```toml
[package]
name = "file-monitor-ebpf"
version = "0.1.0"
edition = "2021"

[dependencies]
aya = { version = "0.12", features = ["async_tokio"] }
aya-log = "0.2"
clap = { version = "4.0", features = ["derive"] }
env_logger = "0.10"
log = "0.4"
tokio = { version = "1.0", features = ["macros", "rt", "rt-multi-thread", "net", "signal", "time"] }
anyhow = "1.0"
bytes = "1.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }

[build-dependencies]
aya-tool = "0.12"

[[bin]]
name = "file-monitor-ebpf"
path = "src/main.rs"
```

## 🔨 Building the Application

### Step 1: Build the Project
```bash
# Build in release mode
cargo build --release

# Or build with debug symbols
cargo build
```

### Step 2: Verify Build
```bash
# Check if binary was created
ls -la target/release/file-monitor-ebpf

# Test the binary was created
ls -la target/release/file-monitor-ebpf
```

## 🏃 Running the Application

### Production Mode (Root Required)
```bash
# Run with root privileges
sudo ./target/release/file-monitor-ebpf

# Run with logging
sudo RUST_LOG=info ./target/release/file-monitor-ebpf

# Run with verbose logging
sudo RUST_LOG=debug ./target/release/file-monitor-ebpf --verbose
```

## 📊 Monitoring Real File Operations

### Terminal 1: Start the Monitor
```bash
sudo ./target/release/file-monitor-ebpf
```

### Terminal 2: Create File Activity
```bash
# Create some files to monitor
echo "test" > /tmp/test.txt
cat /etc/passwd > /dev/null
rm /tmp/test.txt
```

### Expected Output
```
[FILE EVENT #1] 2024-01-15 14:30:22 UTC - WRITE by user (PID: 1234, UID: 1000, GID: 1000): /tmp/test.txt
[FILE EVENT #2] 2024-01-15 14:30:23 UTC - OPEN by user (PID: 1234, UID: 1000, GID: 1000): /etc/passwd
[FILE EVENT #3] 2024-01-15 14:30:24 UTC - DELETE by user (PID: 1234, UID: 1000, GID: 1000): /tmp/test.txt
```

## 🔍 Troubleshooting

### Common Issues and Solutions

#### 1. Permission Denied
```bash
# Error: Permission denied to load eBPF program
# Solution: Run with sudo
sudo ./target/release/file-monitor-ebpf
```

#### 2. Kernel Too Old
```bash
# Error: Kernel version not supported
# Solution: Update kernel
sudo apt update && sudo apt upgrade
# Reboot if kernel was updated
```

#### 3. Missing Dependencies
```bash
# Error: clang not found
# Solution: Install development tools
sudo apt install build-essential clang llvm
```

#### 4. eBPF Program Load Failed
```bash
# Error: Failed to load eBPF program
# Solution: Check kernel headers
sudo apt install linux-headers-$(uname -r)
```

#### 5. Rust Compilation Errors
```bash
# Error: Rust compilation failed
# Solution: Update Rust and add target
rustup update
rustup target add bpfel-unknown-none
```



## 🔧 Advanced Configuration

### Environment Variables
```bash
# Set log level
export RUST_LOG=debug

# Set output format
export MONITOR_OUTPUT=json

# Set filter rules
export MONITOR_FILTER="*.log,*.txt"
```

### Systemd Service (Optional)
Create `/etc/systemd/system/file-monitor.service`:
```ini
[Unit]
Description=eBPF File Monitor
After=network.target

[Service]
Type=simple
User=root
ExecStart=/path/to/file-monitor-ebpf
Restart=on-failure

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable file-monitor
sudo systemctl start file-monitor
```

## 📈 Performance Tuning

### For High-Volume Systems
```bash
# Increase eBPF memory limits
echo 'net.core.bpf_jit_enable = 1' >> /etc/sysctl.conf
echo 'net.core.bpf_jit_harden = 0' >> /etc/sysctl.conf
sysctl -p
```

### Memory Usage
```bash
# Monitor memory usage
top -p $(pgrep file-monitor-ebpf)

# Check eBPF memory usage
sudo bpftool prog list
sudo bpftool map list
```

## 📚 Additional Resources

- [eBPF Documentation](https://ebpf.io/)
- [Aya Framework](https://github.com/aya-rs/aya)
- [Ubuntu eBPF Guide](https://ubuntu.com/blog/what-is-ebpf)
- [Kernel Documentation](https://www.kernel.org/doc/html/latest/bpf/index.html)

## 🆘 Getting Help

If you encounter issues:

1. Check the [Troubleshooting](#troubleshooting) section
2. Verify your system meets the [Prerequisites](#prerequisites)
3. Run the system check: `./ubuntu_setup.sh`
4. Check kernel logs: `sudo dmesg | grep -i bpf`
5. Verify eBPF support: `sudo bpftool feature`

## 🎯 Next Steps

Once installed and running:

1. **Test the build** to verify installation
2. **Run production mode** with root privileges
3. **Monitor real file operations** in your environment
4. **Integrate with logging systems** using JSON output
5. **Set up alerts** for suspicious file activity

---

**Note**: This application provides powerful system monitoring capabilities. Use responsibly and in compliance with your organization's security policies.
