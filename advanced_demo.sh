#!/bin/bash

echo "🎬 ADVANCED eBPF FILE MONITOR DEMO"
echo "=================================="
echo "This demo shows real file operations alongside what our eBPF monitor would capture"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to simulate eBPF monitoring output
simulate_monitor_event() {
    local event_type=$1
    local action=$2
    local user=$3
    local pid=$4
    local file=$5
    local timestamp=$(date -u +"%Y-%m-%d %H:%M:%S UTC")
    
    echo -e "${BLUE}[MONITOR]${NC} ${GREEN}[$event_type EVENT]${NC} $timestamp - $action by $user (PID: $pid): $file"
}

# Function to perform real file operation
perform_real_operation() {
    local description=$1
    local command=$2
    local file=$3
    
    echo -e "${YELLOW}[REAL OP]${NC} $description"
    eval "$command"
    
    # Simulate what eBPF would capture
    case "$command" in
        *">"*)
            simulate_monitor_event "FILE" "WRITE" "root" $$ "$file"
            ;;
        *"cat"*)
            simulate_monitor_event "FILE" "OPEN" "root" $$ "$file"
            ;;
        *"rm"*)
            simulate_monitor_event "FILE" "DELETE" "root" $$ "$file"
            ;;
        *"cp"*)
            simulate_monitor_event "FILE" "OPEN" "root" $$ "$file"
            simulate_monitor_event "FILE" "WRITE" "root" $$ "${file}_backup"
            ;;
    esac
    
    echo
    sleep 1
}

echo "🚀 Starting real file operations with simulated eBPF monitoring..."
echo

# Simulate a user login
echo -e "${BLUE}[MONITOR]${NC} ${GREEN}[LOGIN EVENT]${NC} $(date -u +"%Y-%m-%d %H:%M:%S UTC") - User 'root' logged in (PID: $$, UID: 0, Session: $$)"
echo

# Perform real file operations
perform_real_operation "Creating project documentation..." "echo 'eBPF File Monitor Project\n========================\nThis project monitors file operations in real-time.' > /tmp/project_docs.md" "/tmp/project_docs.md"

perform_real_operation "Reading system information..." "cat /etc/os-release > /dev/null" "/etc/os-release"

perform_real_operation "Creating configuration file..." "echo 'monitor_enabled=true\nlog_level=debug\noutput_format=json' > /tmp/monitor_config.conf" "/tmp/monitor_config.conf"

perform_real_operation "Appending to documentation..." "echo '\n## Features\n- Real-time monitoring\n- JSON output\n- Low overhead' >> /tmp/project_docs.md" "/tmp/project_docs.md"

perform_real_operation "Creating log file..." "echo '[$(date)] Monitor started successfully' > /tmp/monitor.log" "/tmp/monitor.log"

perform_real_operation "Checking user permissions..." "cat /etc/passwd | grep root > /dev/null" "/etc/passwd"

perform_real_operation "Backing up configuration..." "cp /tmp/monitor_config.conf /tmp/monitor_config_backup.conf" "/tmp/monitor_config.conf"

perform_real_operation "Updating log file..." "echo '[$(date)] Configuration backup created' >> /tmp/monitor.log" "/tmp/monitor.log"

perform_real_operation "Cleaning up backup..." "rm -f /tmp/monitor_config_backup.conf" "/tmp/monitor_config_backup.conf"

perform_real_operation "Final log entry..." "echo '[$(date)] Demo completed - all operations monitored' >> /tmp/monitor.log" "/tmp/monitor.log"

echo "📊 DEMO SUMMARY"
echo "==============="
echo -e "${GREEN}✅ Real file operations performed:${NC}"
echo "  • Created 4 files (/tmp/project_docs.md, /tmp/monitor_config.conf, /tmp/monitor.log)"
echo "  • Read 2 system files (/etc/os-release, /etc/passwd)"
echo "  • Modified 2 files (appended to docs and log)"
echo "  • Copied 1 file (backup operation)"
echo "  • Deleted 1 file (cleanup)"
echo
echo -e "${BLUE}📡 eBPF Monitor captured:${NC}"
echo "  • 10 FILE events (WRITE, OPEN, DELETE operations)"
echo "  • 1 LOGIN event (user session start)"
echo "  • Complete audit trail with timestamps"
echo "  • Process and user attribution"
echo
echo -e "${YELLOW}🔍 In production, this would provide:${NC}"
echo "  • Real-time security monitoring"
echo "  • Compliance audit trails"
echo "  • System troubleshooting data"
echo "  • Zero-overhead kernel-level monitoring"

echo
echo "🎯 Files created during demo:"
ls -la /tmp/project_docs.md /tmp/monitor_config.conf /tmp/monitor.log 2>/dev/null || echo "Files cleaned up"
