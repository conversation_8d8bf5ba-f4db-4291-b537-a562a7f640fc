#!/bin/bash

echo "🎬 INTERACTIVE FILE OPERATIONS DEMO"
echo "==================================="
echo "This script will perform real file operations while monitoring"
echo

# Function to create a file operation and show what we're doing
perform_operation() {
    echo "📝 $1"
    eval "$2"
    sleep 1
}

# Create some realistic file operations
perform_operation "Creating a new document..." "echo 'Hello World! This is a test document.' > /tmp/test_document.txt"

perform_operation "Reading system configuration..." "cat /etc/hostname > /dev/null"

perform_operation "Creating a config file..." "echo 'server_port=8080\ndatabase_url=localhost:5432' > /tmp/app_config.conf"

perform_operation "Editing the document..." "echo 'Adding more content to the document.' >> /tmp/test_document.txt"

perform_operation "Creating a log file..." "echo '[$(date)] Application started' > /tmp/app.log"

perform_operation "Checking system files..." "cat /etc/passwd | head -5 > /dev/null"

perform_operation "Creating a backup..." "cp /tmp/test_document.txt /tmp/test_document_backup.txt"

perform_operation "Updating the log..." "echo '[$(date)] User performed backup operation' >> /tmp/app.log"

perform_operation "Cleaning up temporary file..." "rm -f /tmp/test_document_backup.txt"

perform_operation "Final log entry..." "echo '[$(date)] Demo completed successfully' >> /tmp/app.log"

echo
echo "✅ File operations completed!"
echo "📊 Summary of operations performed:"
echo "  • Created 4 files"
echo "  • Read 2 system files"
echo "  • Modified 2 files"
echo "  • Deleted 1 file"
echo "  • Copied 1 file"
echo
echo "In a real eBPF environment, each operation would be captured by the monitor!"
