# eBPF File Monitor - VM Deployment Guide

## Quick Deployment (Automated)

1. **Run the deployment script from your local machine:**
   ```bash
   ./deploy_to_vm.sh
   ```

2. **SSH into your VM:**
   ```bash
   ssh -i /Users/<USER>/Downloads/awskeypair.pem ubuntu@***********
   ```

3. **Run the setup script on the VM:**
   ```bash
   chmod +x setup_on_vm.sh
   ./setup_on_vm.sh
   ```

4. **Run the file monitor:**
   ```bash
   sudo ./target/release/file-monitor-ebpf
   ```

## Manual Deployment (Step by Step)

### Step 1: Copy Files to VM
```bash
# From your local machine
scp -i /Users/<USER>/Downloads/awskeypair.pem -r \
    Cargo.toml build.rs Makefile src/ \
    ubuntu@***********:~/file-monitor-ebpf/
```

### Step 2: SSH into VM
```bash
ssh -i /Users/<USER>/Downloads/awskeypair.pem ubuntu@***********
```

### Step 3: Install Dependencies on VM
```bash
# Update system
sudo apt update

# Install build dependencies
sudo apt install -y \
    build-essential \
    clang \
    llvm \
    libbpf-dev \
    linux-headers-$(uname -r) \
    pkg-config \
    libssl-dev \
    curl \
    git

# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
source ~/.cargo/env

# Add eBPF target
rustup target add bpfel-unknown-none

# Install bpf-linker
cargo install bpf-linker
```

### Step 4: Build the Project
```bash
cd ~/file-monitor-ebpf
cargo build --release
```

### Step 5: Run the File Monitor
```bash
# Basic run
sudo ./target/release/file-monitor-ebpf

# With verbose output
sudo ./target/release/file-monitor-ebpf --verbose

# With JSON output
sudo ./target/release/file-monitor-ebpf --output json

# Filter specific file types
sudo ./target/release/file-monitor-ebpf --filter "txt,log,conf"

# Monitor only user accounts (UID >= 1000)
sudo ./target/release/file-monitor-ebpf --min-uid 1000
```

## Command Line Options

- `-v, --verbose`: Enable verbose output with JSON logging
- `-f, --filter <EXTENSIONS>`: Filter events by file extension (e.g., 'txt,log,conf')
- `-o, --output <FORMAT>`: Output format: text or json (default: text)
- `--min-uid <UID>`: Minimum UID to monitor (default: 1000)

## Testing the Monitor

Once running, you can test it by performing file operations in another terminal:

```bash
# In another SSH session to the VM
echo "test" > /tmp/test.txt
echo "more data" >> /tmp/test.txt
rm /tmp/test.txt
```

You should see these operations logged by the file monitor.

## Troubleshooting

### Build Errors
- Ensure all dependencies are installed
- Check that you have the correct kernel headers: `uname -r`
- Verify clang is available: `clang --version`

### Runtime Errors
- Must run with sudo: `sudo ./target/release/file-monitor-ebpf`
- Check kernel version: `uname -r` (requires 4.18+)
- Verify eBPF support: `ls /sys/kernel/debug/tracing/`

### Permission Issues
- Ensure the VM user has sudo access
- Check that the SSH key has correct permissions (600)
