#!/bin/bash

echo "🔧 Fixing Rust toolchain for eBPF development"
echo "=============================================="

# Install nightly toolchain
echo "🌙 Installing Rust nightly toolchain..."
rustup install nightly

# Set nightly as default
echo "🎯 Setting nightly as default toolchain..."
rustup default nightly

# Now add the eBPF target
echo "📦 Adding eBPF target..."
rustup target add bpfel-unknown-none

# Install bpf-linker if not already installed
if ! command -v bpf-linker &> /dev/null; then
    echo "🔗 Installing bpf-linker..."
    cargo install bpf-linker
fi

echo "✅ Rust nightly toolchain configured successfully!"
echo ""
echo "🔨 Now you can build the project:"
echo "cargo build --release"
echo ""
echo "🏃 And run the file monitor:"
echo "sudo ./target/release/file-monitor-ebpf"
